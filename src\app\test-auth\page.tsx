'use client';

import { useState, useEffect } from 'react';

export default function TestAuthPage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        setError('Not authenticated');
      }
    } catch (err) {
      setError('Error checking auth');
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        }),
      });

      const data = await response.json();
      console.log('Login response:', data);
      
      if (response.ok) {
        setUser(data.user);
        setError('');
      } else {
        setError(data.error || 'Login failed');
      }
    } catch (err) {
      setError('Login error');
    }
  };

  if (loading) return <div className="p-8">Loading...</div>;

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4">Auth Test Page</h1>
      
      {user ? (
        <div className="bg-green-100 p-4 rounded mb-4">
          <h2 className="font-semibold">Authenticated!</h2>
          <p>Email: {user.email}</p>
          <p>Admin: {user.isAdmin ? 'Yes' : 'No'}</p>
          <p>ID: {user.id}</p>
        </div>
      ) : (
        <div className="bg-red-100 p-4 rounded mb-4">
          <h2 className="font-semibold">Not Authenticated</h2>
          {error && <p>Error: {error}</p>}
        </div>
      )}

      <button
        onClick={testLogin}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Test Login
      </button>

      <button
        onClick={checkAuth}
        className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 ml-2"
      >
        Check Auth
      </button>
    </div>
  );
}
