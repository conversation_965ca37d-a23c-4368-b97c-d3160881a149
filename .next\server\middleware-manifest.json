{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "u0PlXW8BZJ3gyUHMDdHv51eYgNVKehwyL3AggvlP82Y=", "__NEXT_PREVIEW_MODE_ID": "381e70c674cff26c5fbb672fab52eb7e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a9e1f63290b58f1039163ab6ea69f9a3f33879dba7d94cf3e56c5f8648e70f13", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "392546b26ac369f968af22315fc082e7f4c9b3e039625bf79a935605b42c0969"}}}, "sortedMiddleware": ["/"], "functions": {}}