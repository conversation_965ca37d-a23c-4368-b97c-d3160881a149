{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "u0PlXW8BZJ3gyUHMDdHv51eYgNVKehwyL3AggvlP82Y=", "__NEXT_PREVIEW_MODE_ID": "714e6689f1c14224b2152e9a1bd27951", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2bf20ae95bc8c4df3b6a719e4f24d6e726a3e41d025a389701fbaa708143d97b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "95b6c9e15b46b396caf2f5f6b37a26b76a5b605470442f8535cf8ba587afc27e"}}}, "sortedMiddleware": ["/"], "functions": {}}