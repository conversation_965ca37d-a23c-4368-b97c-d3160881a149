{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0c5b52f1._.js", "server/edge/chunks/[root-of-the-server]__aa023adf._.js", "server/edge/chunks/edge-wrapper_ba1c96a8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "u0PlXW8BZJ3gyUHMDdHv51eYgNVKehwyL3AggvlP82Y=", "__NEXT_PREVIEW_MODE_ID": "18f51e57b4ceaebeb859e2816a92f44a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5449f231e254daf6d7ce48a297fc2c4725969e312f9f40f1504a5f3e89d2bce4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "324ace57659739eb8b70a66d105d35306abfaa0a7b28eedaa065783f9dbe23ea"}}}, "sortedMiddleware": ["/"], "functions": {}}