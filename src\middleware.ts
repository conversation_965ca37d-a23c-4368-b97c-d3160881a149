import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Simple JWT verification for middleware (Edge runtime compatible)
function verifyTokenSimple(token: string): any {
  try {
    const jwt = require('jsonwebtoken');
    const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export function middleware(request: NextRequest) {
  // Temporarily disable middleware for debugging
  console.log('Middleware bypassed for:', request.nextUrl.pathname);
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
