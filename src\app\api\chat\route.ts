import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';
import { sql } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      );
    }
    
    const payload = verifyToken(token);
    
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }
    
    const { question, sessionId } = await request.json();
    
    if (!question) {
      return NextResponse.json(
        { error: 'Question is required' },
        { status: 400 }
      );
    }
    
    // Call Cobra AI API
    const cobraResponse = await fetch(
      'https://studio.cobraaisystems.com/api/v1/prediction/d09df7a5-80a2-4c8e-8768-c3b4d8c5b5c1',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question }),
      }
    );
    
    if (!cobraResponse.ok) {
      throw new Error('Failed to get response from Cobra AI');
    }
    
    const aiResponse = await cobraResponse.json();
    const responseText = aiResponse.text || aiResponse.response || 'No response received';
    
    // Save to database if sessionId is provided
    if (sessionId) {
      await sql`
        INSERT INTO chat_messages (session_id, message, response)
        VALUES (${sessionId}, ${question}, ${responseText})
      `;
    }
    
    return NextResponse.json({
      response: responseText,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Failed to process chat message' },
      { status: 500 }
    );
  }
}
