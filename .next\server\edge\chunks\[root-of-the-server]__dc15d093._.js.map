{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Public routes that don't require authentication\n  const publicRoutes = ['/', '/login', '/api/auth/login', '/api/auth/logout', '/api/auth/update-profile'];\n\n  // Admin-only routes\n  const adminRoutes = ['/admin'];\n\n  // Check if the route is public\n  if (publicRoutes.includes(pathname)) {\n    return NextResponse.next();\n  }\n\n  // Get token from cookies\n  const token = request.cookies.get('auth-token')?.value;\n\n  if (!token) {\n    // Redirect to login if no token\n    return NextResponse.redirect(new URL('/login', request.url));\n  }\n\n  // For Edge runtime compatibility, we'll do basic token presence check\n  // The actual JWT verification will be done in the API routes\n  if (token && token.length > 10) {\n    // Token exists and looks valid, allow access\n    // Admin route checking will be done client-side and in API routes\n    return NextResponse.next();\n  } else {\n    // Invalid token format, redirect to login\n    const response = NextResponse.redirect(new URL('/login', request.url));\n    response.cookies.delete('auth-token');\n    return response;\n  }\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,kDAAkD;IAClD,MAAM,eAAe;QAAC;QAAK;QAAU;QAAmB;QAAoB;KAA2B;IAEvG,oBAAoB;IACpB,MAAM,cAAc;QAAC;KAAS;IAE9B,+BAA+B;IAC/B,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,yBAAyB;IACzB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAEjD,IAAI,CAAC,OAAO;QACV,gCAAgC;QAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,sEAAsE;IACtE,6DAA6D;IAC7D,IAAI,SAAS,MAAM,MAAM,GAAG,IAAI;QAC9B,6CAA6C;QAC7C,kEAAkE;QAClE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B,OAAO;QACL,0CAA0C;QAC1C,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QACpE,SAAS,OAAO,CAAC,MAAM,CAAC;QACxB,OAAO;IACT;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}