{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/app/api/auth/logout/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nexport async function POST() {\n  try {\n    const response = NextResponse.json({ success: true });\n    \n    // Clear the auth token cookie\n    response.cookies.set('auth-token', '', {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'lax',\n      maxAge: 0,\n    });\n    \n    return response;\n  } catch (error) {\n    console.error('Logout error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;QAEnD,8BAA8B;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI;YACrC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ;QACV;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}