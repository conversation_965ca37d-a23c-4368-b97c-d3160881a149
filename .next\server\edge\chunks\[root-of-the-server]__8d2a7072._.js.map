{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Simple JWT verification for middleware (Edge runtime compatible)\nfunction verifyTokenSimple(token: string): any {\n  try {\n    const jwt = require('jsonwebtoken');\n    const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n    return jwt.verify(token, JWT_SECRET);\n  } catch (error) {\n    return null;\n  }\n}\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Public routes that don't require authentication\n  const publicRoutes = ['/', '/login', '/api/auth/login', '/api/auth/logout'];\n\n  // Admin-only routes\n  const adminRoutes = ['/admin'];\n\n  // Check if the route is public\n  if (publicRoutes.includes(pathname)) {\n    return NextResponse.next();\n  }\n\n  // Get token from cookies\n  const token = request.cookies.get('auth-token')?.value;\n\n  if (!token) {\n    // Redirect to login if no token\n    return NextResponse.redirect(new URL('/login', request.url));\n  }\n\n  // Simple token verification for middleware\n  try {\n    const jwt = require('jsonwebtoken');\n    const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n    const payload = jwt.verify(token, JWT_SECRET);\n\n    // Check admin routes\n    if (adminRoutes.some(route => pathname.startsWith(route))) {\n      if (!payload.isAdmin) {\n        return NextResponse.redirect(new URL('/chat', request.url));\n      }\n    }\n\n    return NextResponse.next();\n  } catch (error) {\n    // Invalid token, redirect to login\n    const response = NextResponse.redirect(new URL('/login', request.url));\n    response.cookies.delete('auth-token');\n    return response;\n  }\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,mEAAmE;AACnE,SAAS,kBAAkB,KAAa;IACtC,IAAI;QACF,MAAM;QACN,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC7C,OAAO,IAAI,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,kDAAkD;IAClD,MAAM,eAAe;QAAC;QAAK;QAAU;QAAmB;KAAmB;IAE3E,oBAAoB;IACpB,MAAM,cAAc;QAAC;KAAS;IAE9B,+BAA+B;IAC/B,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,yBAAyB;IACzB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAEjD,IAAI,CAAC,OAAO;QACV,gCAAgC;QAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,2CAA2C;IAC3C,IAAI;QACF,MAAM;QACN,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC7C,MAAM,UAAU,IAAI,MAAM,CAAC,OAAO;QAElC,qBAAqB;QACrB,IAAI,YAAY,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;YACzD,IAAI,CAAC,QAAQ,OAAO,EAAE;gBACpB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,SAAS,QAAQ,GAAG;YAC3D;QACF;QAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B,EAAE,OAAO,OAAO;QACd,mCAAmC;QACnC,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QACpE,SAAS,OAAO,CAAC,MAAM,CAAC;QACxB,OAAO;IACT;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}