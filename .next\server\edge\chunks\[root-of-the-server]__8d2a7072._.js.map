{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Simple JWT verification for middleware (Edge runtime compatible)\nfunction verifyTokenSimple(token: string): any {\n  try {\n    const jwt = require('jsonwebtoken');\n    const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n    return jwt.verify(token, JWT_SECRET);\n  } catch (error) {\n    return null;\n  }\n}\n\nexport function middleware(request: NextRequest) {\n  // Temporarily disable middleware for debugging\n  console.log('Middleware bypassed for:', request.nextUrl.pathname);\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,mEAAmE;AACnE,SAAS,kBAAkB,KAAa;IACtC,IAAI;QACF,MAAM;QACN,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC7C,OAAO,IAAI,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,WAAW,OAAoB;IAC7C,+CAA+C;IAC/C,QAAQ,GAAG,CAAC,4BAA4B,QAAQ,OAAO,CAAC,QAAQ;IAChE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}