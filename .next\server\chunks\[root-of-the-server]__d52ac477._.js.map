{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/lib/db.ts"], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\n\nif (!process.env.DATABASE_URL) {\n  throw new Error('DATABASE_URL is not defined');\n}\n\nexport const sql = neon(process.env.DATABASE_URL);\n\n// Database initialization function\nexport async function initializeDatabase() {\n  try {\n    // Create users table\n    await sql`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        is_admin BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create chat_sessions table\n    await sql`\n      CREATE TABLE IF NOT EXISTS chat_sessions (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,\n        title VARCHAR(255) NOT NULL DEFAULT 'New Chat',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create chat_messages table\n    await sql`\n      CREATE TABLE IF NOT EXISTS chat_messages (\n        id SERIAL PRIMARY KEY,\n        session_id INTEGER REFERENCES chat_sessions(id) ON DELETE CASCADE,\n        message TEXT NOT NULL,\n        response TEXT,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create indexes for better performance\n    await sql`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`;\n    await sql`CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id)`;\n    await sql`CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id)`;\n\n    console.log('Database initialized successfully');\n  } catch (error) {\n    console.error('Error initializing database:', error);\n    throw error;\n  }\n}\n\n// Helper function to create admin user if it doesn't exist\nexport async function createAdminUser() {\n  const bcrypt = require('bcryptjs');\n\n  try {\n    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n\n    // Check if ANY admin user already exists\n    const existingAdmin = await sql`\n      SELECT id FROM users WHERE is_admin = true\n    `;\n\n    if (existingAdmin.length === 0) {\n      const hashedPassword = await bcrypt.hash(adminPassword, 12);\n\n      await sql`\n        INSERT INTO users (email, password_hash, is_admin)\n        VALUES (${adminEmail}, ${hashedPassword}, true)\n      `;\n\n      console.log('Admin user created successfully');\n    } else {\n      console.log('Admin user already exists');\n    }\n  } catch (error) {\n    console.error('Error creating admin user:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;IAC7B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AAGzC,eAAe;IACpB,IAAI;QACF,qBAAqB;QACrB,MAAM,GAAG,CAAC;;;;;;;;;IASV,CAAC;QAED,6BAA6B;QAC7B,MAAM,GAAG,CAAC;;;;;;;;IAQV,CAAC;QAED,6BAA6B;QAC7B,MAAM,GAAG,CAAC;;;;;;;;IAQV,CAAC;QAED,wCAAwC;QACxC,MAAM,GAAG,CAAC,0DAA0D,CAAC;QACrE,MAAM,GAAG,CAAC,8EAA8E,CAAC;QACzF,MAAM,GAAG,CAAC,oFAAoF,CAAC;QAE/F,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe;IACpB,MAAM;IAEN,IAAI;QACF,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC9C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,yCAAyC;QACzC,MAAM,gBAAgB,MAAM,GAAG,CAAC;;IAEhC,CAAC;QAED,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM,iBAAiB,MAAM,OAAO,IAAI,CAAC,eAAe;YAExD,MAAM,GAAG,CAAC;;gBAEA,EAAE,WAAW,EAAE,EAAE,eAAe;MAC1C,CAAC;YAED,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { sql } from './db';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface User {\n  id: number;\n  email: string;\n  is_admin: boolean;\n}\n\nexport interface JWTPayload {\n  userId: number;\n  email: string;\n  isAdmin: boolean;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token\nexport function generateToken(user: User): string {\n  const payload: JWTPayload = {\n    userId: user.id,\n    email: user.email,\n    isAdmin: user.is_admin,\n  };\n  \n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Verify JWT token\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Authenticate user\nexport async function authenticateUser(email: string, password: string): Promise<User | null> {\n  try {\n    const users = await sql`\n      SELECT id, email, password_hash, is_admin \n      FROM users \n      WHERE email = ${email}\n    `;\n    \n    if (users.length === 0) {\n      return null;\n    }\n    \n    const user = users[0];\n    const isValidPassword = await verifyPassword(password, user.password_hash);\n    \n    if (!isValidPassword) {\n      return null;\n    }\n    \n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Authentication error:', error);\n    return null;\n  }\n}\n\n// Get user by ID\nexport async function getUserById(id: number): Promise<User | null> {\n  try {\n    const users = await sql`\n      SELECT id, email, is_admin \n      FROM users \n      WHERE id = ${id}\n    `;\n    \n    if (users.length === 0) {\n      return null;\n    }\n    \n    const user = users[0];\n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Get user error:', error);\n    return null;\n  }\n}\n\n// Create new user (admin only)\nexport async function createUser(email: string, password: string, isAdmin: boolean = false): Promise<User | null> {\n  try {\n    const hashedPassword = await hashPassword(password);\n    \n    const result = await sql`\n      INSERT INTO users (email, password_hash, is_admin)\n      VALUES (${email}, ${hashedPassword}, ${isAdmin})\n      RETURNING id, email, is_admin\n    `;\n    \n    if (result.length === 0) {\n      return null;\n    }\n    \n    const user = result[0];\n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Create user error:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAetC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,IAAU;IACtC,MAAM,UAAsB;QAC1B,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;QACjB,SAAS,KAAK,QAAQ;IACxB;IAEA,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,iBAAiB,KAAa,EAAE,QAAgB;IACpE,IAAI;QACF,MAAM,QAAQ,MAAM,kHAAA,CAAA,MAAG,CAAC;;;oBAGR,EAAE,MAAM;IACxB,CAAC;QAED,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,kBAAkB,MAAM,eAAe,UAAU,KAAK,aAAa;QAEzE,IAAI,CAAC,iBAAiB;YACpB,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAGO,eAAe,YAAY,EAAU;IAC1C,IAAI;QACF,MAAM,QAAQ,MAAM,kHAAA,CAAA,MAAG,CAAC;;;iBAGX,EAAE,GAAG;IAClB,CAAC;QAED,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;IACT;AACF;AAGO,eAAe,WAAW,KAAa,EAAE,QAAgB,EAAE,UAAmB,KAAK;IACxF,IAAI;QACF,MAAM,iBAAiB,MAAM,aAAa;QAE1C,MAAM,SAAS,MAAM,kHAAA,CAAA,MAAG,CAAC;;cAEf,EAAE,MAAM,EAAE,EAAE,eAAe,EAAE,EAAE,QAAQ;;IAEjD,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/lib/validation.ts"], "sourcesContent": ["// Input validation utilities\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): { isValid: boolean; errors: string[] } {\n  const errors: string[] = [];\n  \n  if (password.length < 6) {\n    errors.push('Password must be at least 6 characters long');\n  }\n  \n  if (password.length > 128) {\n    errors.push('Password must be less than 128 characters');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.trim().replace(/[<>]/g, '');\n}\n\nexport function validateChatMessage(message: string): { isValid: boolean; error?: string } {\n  if (!message || message.trim().length === 0) {\n    return { isValid: false, error: 'Message cannot be empty' };\n  }\n  \n  if (message.length > 4000) {\n    return { isValid: false, error: 'Message is too long (max 4000 characters)' };\n  }\n  \n  return { isValid: true };\n}\n\nexport function validateSessionTitle(title: string): { isValid: boolean; error?: string } {\n  if (!title || title.trim().length === 0) {\n    return { isValid: false, error: 'Title cannot be empty' };\n  }\n  \n  if (title.length > 100) {\n    return { isValid: false, error: 'Title is too long (max 100 characters)' };\n  }\n  \n  return { isValid: true };\n}\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;;;AAEtB,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,SAAS,oBAAoB,OAAe;IACjD,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,IAAI,QAAQ,MAAM,GAAG,MAAM;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,SAAS,qBAAqB,KAAa;IAChD,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,KAAK,GAAG;QACvC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAwB;IAC1D;IAEA,IAAI,MAAM,MAAM,GAAG,KAAK;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyC;IAC3E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/app/api/auth/update-profile/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { verifyToken, hashPassword } from '@/lib/auth';\nimport { sql } from '@/lib/db';\nimport { validateEmail, validatePassword } from '@/lib/validation';\n\nexport async function PATCH(request: NextRequest) {\n  try {\n    const token = request.cookies.get('auth-token')?.value;\n    \n    if (!token) {\n      return NextResponse.json(\n        { error: 'No token provided' },\n        { status: 401 }\n      );\n    }\n    \n    const payload = verifyToken(token);\n    \n    if (!payload) {\n      return NextResponse.json(\n        { error: 'Invalid token' },\n        { status: 401 }\n      );\n    }\n    \n    const { email, password, currentPassword } = await request.json();\n\n    // Validate that at least one field is provided\n    if (!email && !password) {\n      return NextResponse.json(\n        { error: 'Email or password is required' },\n        { status: 400 }\n      );\n    }\n\n    // If updating password, validate current password first\n    if (password) {\n      if (!currentPassword) {\n        return NextResponse.json(\n          { error: 'Current password is required to update password' },\n          { status: 400 }\n        );\n      }\n\n      // Verify current password\n      const userResult = await sql`\n        SELECT password_hash FROM users WHERE id = ${payload.userId}\n      `;\n\n      if (userResult.length === 0) {\n        return NextResponse.json(\n          { error: 'User not found' },\n          { status: 404 }\n        );\n      }\n\n      const bcrypt = require('bcryptjs');\n      const isValidPassword = await bcrypt.compare(currentPassword, userResult[0].password_hash);\n\n      if (!isValidPassword) {\n        return NextResponse.json(\n          { error: 'Current password is incorrect' },\n          { status: 400 }\n        );\n      }\n\n      // Validate new password\n      const passwordValidation = validatePassword(password);\n      if (!passwordValidation.isValid) {\n        return NextResponse.json(\n          { error: passwordValidation.errors.join(', ') },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Validate email if provided\n    if (email) {\n      if (!validateEmail(email)) {\n        return NextResponse.json(\n          { error: 'Invalid email format' },\n          { status: 400 }\n        );\n      }\n\n      // Check if email is already taken by another user\n      const existingUsers = await sql`\n        SELECT id FROM users\n        WHERE email = ${email} AND id != ${payload.userId}\n      `;\n\n      if (existingUsers.length > 0) {\n        return NextResponse.json(\n          { error: 'Email is already taken' },\n          { status: 409 }\n        );\n      }\n    }\n\n    // Update user based on provided fields\n    let result;\n\n    if (email && password) {\n      const hashedPassword = await hashPassword(password);\n      result = await sql`\n        UPDATE users\n        SET email = ${email}, password_hash = ${hashedPassword}, updated_at = CURRENT_TIMESTAMP\n        WHERE id = ${payload.userId}\n        RETURNING id, email, is_admin\n      `;\n    } else if (email) {\n      result = await sql`\n        UPDATE users\n        SET email = ${email}, updated_at = CURRENT_TIMESTAMP\n        WHERE id = ${payload.userId}\n        RETURNING id, email, is_admin\n      `;\n    } else if (password) {\n      const hashedPassword = await hashPassword(password);\n      result = await sql`\n        UPDATE users\n        SET password_hash = ${hashedPassword}, updated_at = CURRENT_TIMESTAMP\n        WHERE id = ${payload.userId}\n        RETURNING id, email, is_admin\n      `;\n    }\n    \n    if (result.length === 0) {\n      return NextResponse.json(\n        { error: 'User not found' },\n        { status: 404 }\n      );\n    }\n    \n    const updatedUser = result[0];\n    \n    return NextResponse.json({\n      success: true,\n      user: {\n        id: updatedUser.id,\n        email: updatedUser.email,\n        isAdmin: updatedUser.is_admin,\n      },\n    });\n  } catch (error) {\n    console.error('Update profile error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,MAAM,OAAoB;IAC9C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAE5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgB,GACzB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/D,+CAA+C;QAC/C,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wDAAwD;QACxD,IAAI,UAAU;YACZ,IAAI,CAAC,iBAAiB;gBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAkD,GAC3D;oBAAE,QAAQ;gBAAI;YAElB;YAEA,0BAA0B;YAC1B,MAAM,aAAa,MAAM,kHAAA,CAAA,MAAG,CAAC;mDACgB,EAAE,QAAQ,MAAM,CAAC;MAC9D,CAAC;YAED,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiB,GAC1B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM;YACN,MAAM,kBAAkB,MAAM,OAAO,OAAO,CAAC,iBAAiB,UAAU,CAAC,EAAE,CAAC,aAAa;YAEzF,IAAI,CAAC,iBAAiB;gBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAgC,GACzC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,wBAAwB;YACxB,MAAM,qBAAqB,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE;YAC5C,IAAI,CAAC,mBAAmB,OAAO,EAAE;gBAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,mBAAmB,MAAM,CAAC,IAAI,CAAC;gBAAM,GAC9C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,6BAA6B;QAC7B,IAAI,OAAO;YACT,IAAI,CAAC,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAuB,GAChC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,kDAAkD;YAClD,MAAM,gBAAgB,MAAM,kHAAA,CAAA,MAAG,CAAC;;sBAEhB,EAAE,MAAM,WAAW,EAAE,QAAQ,MAAM,CAAC;MACpD,CAAC;YAED,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyB,GAClC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,uCAAuC;QACvC,IAAI;QAEJ,IAAI,SAAS,UAAU;YACrB,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;YAC1C,SAAS,MAAM,kHAAA,CAAA,MAAG,CAAC;;oBAEL,EAAE,MAAM,kBAAkB,EAAE,eAAe;mBAC5C,EAAE,QAAQ,MAAM,CAAC;;MAE9B,CAAC;QACH,OAAO,IAAI,OAAO;YAChB,SAAS,MAAM,kHAAA,CAAA,MAAG,CAAC;;oBAEL,EAAE,MAAM;mBACT,EAAE,QAAQ,MAAM,CAAC;;MAE9B,CAAC;QACH,OAAO,IAAI,UAAU;YACnB,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;YAC1C,SAAS,MAAM,kHAAA,CAAA,MAAG,CAAC;;4BAEG,EAAE,eAAe;mBAC1B,EAAE,QAAQ,MAAM,CAAC;;MAE9B,CAAC;QACH;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,MAAM,CAAC,EAAE;QAE7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,IAAI,YAAY,EAAE;gBAClB,OAAO,YAAY,KAAK;gBACxB,SAAS,YAAY,QAAQ;YAC/B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}