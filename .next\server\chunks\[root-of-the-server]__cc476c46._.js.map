{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/lib/db.ts"], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\n\nif (!process.env.DATABASE_URL) {\n  throw new Error('DATABASE_URL is not defined');\n}\n\nexport const sql = neon(process.env.DATABASE_URL);\n\n// Database initialization function\nexport async function initializeDatabase() {\n  try {\n    // Create users table\n    await sql`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        is_admin BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create chat_sessions table\n    await sql`\n      CREATE TABLE IF NOT EXISTS chat_sessions (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,\n        title VARCHAR(255) NOT NULL DEFAULT 'New Chat',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create chat_messages table\n    await sql`\n      CREATE TABLE IF NOT EXISTS chat_messages (\n        id SERIAL PRIMARY KEY,\n        session_id INTEGER REFERENCES chat_sessions(id) ON DELETE CASCADE,\n        message TEXT NOT NULL,\n        response TEXT,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create indexes for better performance\n    await sql`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`;\n    await sql`CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id)`;\n    await sql`CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id)`;\n\n    console.log('Database initialized successfully');\n  } catch (error) {\n    console.error('Error initializing database:', error);\n    throw error;\n  }\n}\n\n// Helper function to create admin user if it doesn't exist\nexport async function createAdminUser() {\n  const bcrypt = require('bcryptjs');\n  \n  try {\n    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    \n    // Check if admin user already exists\n    const existingAdmin = await sql`\n      SELECT id FROM users WHERE email = ${adminEmail} AND is_admin = true\n    `;\n    \n    if (existingAdmin.length === 0) {\n      const hashedPassword = await bcrypt.hash(adminPassword, 12);\n      \n      await sql`\n        INSERT INTO users (email, password_hash, is_admin)\n        VALUES (${adminEmail}, ${hashedPassword}, true)\n      `;\n      \n      console.log('Admin user created successfully');\n    } else {\n      console.log('Admin user already exists');\n    }\n  } catch (error) {\n    console.error('Error creating admin user:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;IAC7B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AAGzC,eAAe;IACpB,IAAI;QACF,qBAAqB;QACrB,MAAM,GAAG,CAAC;;;;;;;;;IASV,CAAC;QAED,6BAA6B;QAC7B,MAAM,GAAG,CAAC;;;;;;;;IAQV,CAAC;QAED,6BAA6B;QAC7B,MAAM,GAAG,CAAC;;;;;;;;IAQV,CAAC;QAED,wCAAwC;QACxC,MAAM,GAAG,CAAC,0DAA0D,CAAC;QACrE,MAAM,GAAG,CAAC,8EAA8E,CAAC;QACzF,MAAM,GAAG,CAAC,oFAAoF,CAAC;QAE/F,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe;IACpB,MAAM;IAEN,IAAI;QACF,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC9C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,qCAAqC;QACrC,MAAM,gBAAgB,MAAM,GAAG,CAAC;yCACK,EAAE,WAAW;IAClD,CAAC;QAED,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM,iBAAiB,MAAM,OAAO,IAAI,CAAC,eAAe;YAExD,MAAM,GAAG,CAAC;;gBAEA,EAAE,WAAW,EAAE,EAAE,eAAe;MAC1C,CAAC;YAED,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { sql } from './db';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface User {\n  id: number;\n  email: string;\n  is_admin: boolean;\n}\n\nexport interface JWTPayload {\n  userId: number;\n  email: string;\n  isAdmin: boolean;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token\nexport function generateToken(user: User): string {\n  const payload: JWTPayload = {\n    userId: user.id,\n    email: user.email,\n    isAdmin: user.is_admin,\n  };\n  \n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Verify JWT token\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Authenticate user\nexport async function authenticateUser(email: string, password: string): Promise<User | null> {\n  try {\n    const users = await sql`\n      SELECT id, email, password_hash, is_admin \n      FROM users \n      WHERE email = ${email}\n    `;\n    \n    if (users.length === 0) {\n      return null;\n    }\n    \n    const user = users[0];\n    const isValidPassword = await verifyPassword(password, user.password_hash);\n    \n    if (!isValidPassword) {\n      return null;\n    }\n    \n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Authentication error:', error);\n    return null;\n  }\n}\n\n// Get user by ID\nexport async function getUserById(id: number): Promise<User | null> {\n  try {\n    const users = await sql`\n      SELECT id, email, is_admin \n      FROM users \n      WHERE id = ${id}\n    `;\n    \n    if (users.length === 0) {\n      return null;\n    }\n    \n    const user = users[0];\n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Get user error:', error);\n    return null;\n  }\n}\n\n// Create new user (admin only)\nexport async function createUser(email: string, password: string, isAdmin: boolean = false): Promise<User | null> {\n  try {\n    const hashedPassword = await hashPassword(password);\n    \n    const result = await sql`\n      INSERT INTO users (email, password_hash, is_admin)\n      VALUES (${email}, ${hashedPassword}, ${isAdmin})\n      RETURNING id, email, is_admin\n    `;\n    \n    if (result.length === 0) {\n      return null;\n    }\n    \n    const user = result[0];\n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Create user error:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAetC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,IAAU;IACtC,MAAM,UAAsB;QAC1B,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;QACjB,SAAS,KAAK,QAAQ;IACxB;IAEA,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,iBAAiB,KAAa,EAAE,QAAgB;IACpE,IAAI;QACF,MAAM,QAAQ,MAAM,kHAAA,CAAA,MAAG,CAAC;;;oBAGR,EAAE,MAAM;IACxB,CAAC;QAED,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,kBAAkB,MAAM,eAAe,UAAU,KAAK,aAAa;QAEzE,IAAI,CAAC,iBAAiB;YACpB,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAGO,eAAe,YAAY,EAAU;IAC1C,IAAI;QACF,MAAM,QAAQ,MAAM,kHAAA,CAAA,MAAG,CAAC;;;iBAGX,EAAE,GAAG;IAClB,CAAC;QAED,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;IACT;AACF;AAGO,eAAe,WAAW,KAAa,EAAE,QAAgB,EAAE,UAAmB,KAAK;IACxF,IAAI;QACF,MAAM,iBAAiB,MAAM,aAAa;QAE1C,MAAM,SAAS,MAAM,kHAAA,CAAA,MAAG,CAAC;;cAEf,EAAE,MAAM,EAAE,EAAE,eAAe,EAAE,EAAE,QAAQ;;IAEjD,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/app/api/auth/me/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { verifyToken, getUserById } from '@/lib/auth';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const token = request.cookies.get('auth-token')?.value;\n    \n    if (!token) {\n      return NextResponse.json(\n        { error: 'No token provided' },\n        { status: 401 }\n      );\n    }\n    \n    const payload = verifyToken(token);\n    \n    if (!payload) {\n      return NextResponse.json(\n        { error: 'Invalid token' },\n        { status: 401 }\n      );\n    }\n    \n    const user = await getUserById(payload.userId);\n    \n    if (!user) {\n      return NextResponse.json(\n        { error: 'User not found' },\n        { status: 404 }\n      );\n    }\n    \n    return NextResponse.json({\n      user: {\n        id: user.id,\n        email: user.email,\n        isAdmin: user.is_admin,\n      },\n    });\n  } catch (error) {\n    console.error('Get user error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAE5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgB,GACzB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM;QAE7C,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,QAAQ;YACxB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}