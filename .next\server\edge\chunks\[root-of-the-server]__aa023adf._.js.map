{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/db.ts"], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\n\nif (!process.env.DATABASE_URL) {\n  throw new Error('DATABASE_URL is not defined');\n}\n\nexport const sql = neon(process.env.DATABASE_URL);\n\n// Database initialization function\nexport async function initializeDatabase() {\n  try {\n    // Create users table\n    await sql`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        is_admin BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create chat_sessions table\n    await sql`\n      CREATE TABLE IF NOT EXISTS chat_sessions (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,\n        title VARCHAR(255) NOT NULL DEFAULT 'New Chat',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create chat_messages table\n    await sql`\n      CREATE TABLE IF NOT EXISTS chat_messages (\n        id SERIAL PRIMARY KEY,\n        session_id INTEGER REFERENCES chat_sessions(id) ON DELETE CASCADE,\n        message TEXT NOT NULL,\n        response TEXT,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `;\n\n    // Create indexes for better performance\n    await sql`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`;\n    await sql`CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id)`;\n    await sql`CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id)`;\n\n    console.log('Database initialized successfully');\n  } catch (error) {\n    console.error('Error initializing database:', error);\n    throw error;\n  }\n}\n\n// Helper function to create admin user if it doesn't exist\nexport async function createAdminUser() {\n  const bcrypt = require('bcryptjs');\n  \n  try {\n    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    \n    // Check if admin user already exists\n    const existingAdmin = await sql`\n      SELECT id FROM users WHERE email = ${adminEmail} AND is_admin = true\n    `;\n    \n    if (existingAdmin.length === 0) {\n      const hashedPassword = await bcrypt.hash(adminPassword, 12);\n      \n      await sql`\n        INSERT INTO users (email, password_hash, is_admin)\n        VALUES (${adminEmail}, ${hashedPassword}, true)\n      `;\n      \n      console.log('Admin user created successfully');\n    } else {\n      console.log('Admin user already exists');\n    }\n  } catch (error) {\n    console.error('Error creating admin user:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;IAC7B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,MAAM,CAAA,GAAA,8JAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AAGzC,eAAe;IACpB,IAAI;QACF,qBAAqB;QACrB,MAAM,GAAG,CAAC;;;;;;;;;IASV,CAAC;QAED,6BAA6B;QAC7B,MAAM,GAAG,CAAC;;;;;;;;IAQV,CAAC;QAED,6BAA6B;QAC7B,MAAM,GAAG,CAAC;;;;;;;;IAQV,CAAC;QAED,wCAAwC;QACxC,MAAM,GAAG,CAAC,0DAA0D,CAAC;QACrE,MAAM,GAAG,CAAC,8EAA8E,CAAC;QACzF,MAAM,GAAG,CAAC,oFAAoF,CAAC;QAE/F,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe;IACpB,MAAM;IAEN,IAAI;QACF,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC9C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,qCAAqC;QACrC,MAAM,gBAAgB,MAAM,GAAG,CAAC;yCACK,EAAE,WAAW;IAClD,CAAC;QAED,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM,iBAAiB,MAAM,OAAO,IAAI,CAAC,eAAe;YAExD,MAAM,GAAG,CAAC;;gBAEA,EAAE,WAAW,EAAE,EAAE,eAAe;MAC1C,CAAC;YAED,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { sql } from './db';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface User {\n  id: number;\n  email: string;\n  is_admin: boolean;\n}\n\nexport interface JWTPayload {\n  userId: number;\n  email: string;\n  isAdmin: boolean;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token\nexport function generateToken(user: User): string {\n  const payload: JWTPayload = {\n    userId: user.id,\n    email: user.email,\n    isAdmin: user.is_admin,\n  };\n  \n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Verify JWT token\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Authenticate user\nexport async function authenticateUser(email: string, password: string): Promise<User | null> {\n  try {\n    const users = await sql`\n      SELECT id, email, password_hash, is_admin \n      FROM users \n      WHERE email = ${email}\n    `;\n    \n    if (users.length === 0) {\n      return null;\n    }\n    \n    const user = users[0];\n    const isValidPassword = await verifyPassword(password, user.password_hash);\n    \n    if (!isValidPassword) {\n      return null;\n    }\n    \n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Authentication error:', error);\n    return null;\n  }\n}\n\n// Get user by ID\nexport async function getUserById(id: number): Promise<User | null> {\n  try {\n    const users = await sql`\n      SELECT id, email, is_admin \n      FROM users \n      WHERE id = ${id}\n    `;\n    \n    if (users.length === 0) {\n      return null;\n    }\n    \n    const user = users[0];\n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Get user error:', error);\n    return null;\n  }\n}\n\n// Create new user (admin only)\nexport async function createUser(email: string, password: string, isAdmin: boolean = false): Promise<User | null> {\n  try {\n    const hashedPassword = await hashPassword(password);\n    \n    const result = await sql`\n      INSERT INTO users (email, password_hash, is_admin)\n      VALUES (${email}, ${hashedPassword}, ${isAdmin})\n      RETURNING id, email, is_admin\n    `;\n    \n    if (result.length === 0) {\n      return null;\n    }\n    \n    const user = result[0];\n    return {\n      id: user.id,\n      email: user.email,\n      is_admin: user.is_admin,\n    };\n  } catch (error) {\n    console.error('Create user error:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAetC,eAAe,aAAa,QAAgB;IACjD,OAAO,yIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,yIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,IAAU;IACtC,MAAM,UAAsB;QAC1B,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;QACjB,SAAS,KAAK,QAAQ;IACxB;IAEA,OAAO,6IAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,6IAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,iBAAiB,KAAa,EAAE,QAAgB;IACpE,IAAI;QACF,MAAM,QAAQ,MAAM,wHAAA,CAAA,MAAG,CAAC;;;oBAGR,EAAE,MAAM;IACxB,CAAC;QAED,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,kBAAkB,MAAM,eAAe,UAAU,KAAK,aAAa;QAEzE,IAAI,CAAC,iBAAiB;YACpB,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAGO,eAAe,YAAY,EAAU;IAC1C,IAAI;QACF,MAAM,QAAQ,MAAM,wHAAA,CAAA,MAAG,CAAC;;;iBAGX,EAAE,GAAG;IAClB,CAAC;QAED,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;IACT;AACF;AAGO,eAAe,WAAW,KAAa,EAAE,QAAgB,EAAE,UAAmB,KAAK;IACxF,IAAI;QACF,MAAM,iBAAiB,MAAM,aAAa;QAE1C,MAAM,SAAS,MAAM,wHAAA,CAAA,MAAG,CAAC;;cAEf,EAAE,MAAM,EAAE,EAAE,eAAe,EAAE,EAAE,QAAQ;;IAEjD,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\nimport { verifyToken } from './lib/auth';\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  \n  // Public routes that don't require authentication\n  const publicRoutes = ['/', '/login', '/api/auth/login'];\n  \n  // Admin-only routes\n  const adminRoutes = ['/admin'];\n  \n  // Protected routes that require authentication\n  const protectedRoutes = ['/chat', '/dashboard'];\n  \n  // Check if the route is public\n  if (publicRoutes.includes(pathname)) {\n    return NextResponse.next();\n  }\n  \n  // Get token from cookies\n  const token = request.cookies.get('auth-token')?.value;\n  \n  if (!token) {\n    // Redirect to login if no token\n    return NextResponse.redirect(new URL('/login', request.url));\n  }\n  \n  // Verify token\n  const payload = verifyToken(token);\n  \n  if (!payload) {\n    // Redirect to login if token is invalid\n    const response = NextResponse.redirect(new URL('/login', request.url));\n    response.cookies.delete('auth-token');\n    return response;\n  }\n  \n  // Check admin routes\n  if (adminRoutes.some(route => pathname.startsWith(route))) {\n    if (!payload.isAdmin) {\n      return NextResponse.redirect(new URL('/chat', request.url));\n    }\n  }\n  \n  // Allow access to protected routes for authenticated users\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,kDAAkD;IAClD,MAAM,eAAe;QAAC;QAAK;QAAU;KAAkB;IAEvD,oBAAoB;IACpB,MAAM,cAAc;QAAC;KAAS;IAE9B,+CAA+C;IAC/C,MAAM,kBAAkB;QAAC;QAAS;KAAa;IAE/C,+BAA+B;IAC/B,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,yBAAyB;IACzB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAEjD,IAAI,CAAC,OAAO;QACV,gCAAgC;QAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,eAAe;IACf,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EAAE;IAE5B,IAAI,CAAC,SAAS;QACZ,wCAAwC;QACxC,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QACpE,SAAS,OAAO,CAAC,MAAM,CAAC;QACxB,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,YAAY,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;QACzD,IAAI,CAAC,QAAQ,OAAO,EAAE;YACpB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,SAAS,QAAQ,GAAG;QAC3D;IACF;IAEA,2DAA2D;IAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}