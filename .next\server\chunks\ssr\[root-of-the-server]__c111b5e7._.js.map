{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  MessageCircle, \n  User, \n  Settings, \n  LogOut, \n  Shield, \n  ChevronDown,\n  Menu,\n  X\n} from 'lucide-react';\n\ninterface User {\n  id: number;\n  email: string;\n  isAdmin: boolean;\n}\n\ninterface HeaderProps {\n  user?: User | null;\n  title?: string;\n  onMenuToggle?: () => void;\n  showMenuButton?: boolean;\n}\n\nexport default function Header({ user, title, onMenuToggle, showMenuButton = false }: HeaderProps) {\n  const [dropdownOpen, setDropdownOpen] = useState(false);\n  const [profileModalOpen, setProfileModalOpen] = useState(false);\n  const [editingEmail, setEditingEmail] = useState(false);\n  const [newEmail, setNewEmail] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    if (user) {\n      setNewEmail(user.email);\n    }\n  }, [user]);\n\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setDropdownOpen(false);\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const handleUpdateEmail = async () => {\n    if (!newEmail.trim() || newEmail === user?.email) {\n      setEditingEmail(false);\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await fetch('/api/auth/update-profile', {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email: newEmail }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setSuccess('Email updated successfully');\n        setEditingEmail(false);\n        // Refresh the page to update user data\n        window.location.reload();\n      } else {\n        setError(data.error || 'Failed to update email');\n      }\n    } catch (error) {\n      setError('An error occurred while updating email');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center\">\n              {showMenuButton && (\n                <button\n                  onClick={onMenuToggle}\n                  className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 mr-2\"\n                >\n                  <Menu className=\"h-5 w-5\" />\n                </button>\n              )}\n              <Link href={user ? (user.isAdmin ? '/admin' : '/chat') : '/'} className=\"flex items-center\">\n                <MessageCircle className=\"h-8 w-8 text-indigo-600 mr-3\" />\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  {title || 'Cobra AI Chat'}\n                </h1>\n              </Link>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <>\n                  {/* Navigation Links */}\n                  <nav className=\"hidden md:flex items-center space-x-4\">\n                    <Link\n                      href=\"/chat\"\n                      className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      Chat\n                    </Link>\n                    {user.isAdmin && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center\"\n                      >\n                        <Shield className=\"h-4 w-4 mr-1\" />\n                        Admin\n                      </Link>\n                    )}\n                  </nav>\n\n                  {/* User Profile Dropdown */}\n                  <div className=\"relative\" ref={dropdownRef}>\n                    <button\n                      onClick={() => setDropdownOpen(!dropdownOpen)}\n                      className=\"flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md px-3 py-2\"\n                    >\n                      <div className=\"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\">\n                        <span className=\"text-sm font-medium text-indigo-600\">\n                          {user.email.charAt(0).toUpperCase()}\n                        </span>\n                      </div>\n                      <span className=\"hidden md:block text-sm font-medium truncate max-w-32\">\n                        {user.email}\n                      </span>\n                      <ChevronDown className=\"h-4 w-4\" />\n                    </button>\n\n                    {dropdownOpen && (\n                      <div className=\"absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50\">\n                        <div className=\"py-1\">\n                          <div className=\"px-4 py-2 border-b border-gray-100\">\n                            <p className=\"text-sm font-medium text-gray-900\">{user.email}</p>\n                            {user.isAdmin && (\n                              <p className=\"text-xs text-indigo-600\">Administrator</p>\n                            )}\n                          </div>\n                          \n                          <button\n                            onClick={() => {\n                              setProfileModalOpen(true);\n                              setDropdownOpen(false);\n                            }}\n                            className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\n                          >\n                            <Settings className=\"h-4 w-4 mr-2\" />\n                            Profile Settings\n                          </button>\n\n                          <div className=\"md:hidden border-t border-gray-100\">\n                            <Link\n                              href=\"/chat\"\n                              className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                              onClick={() => setDropdownOpen(false)}\n                            >\n                              Chat\n                            </Link>\n                            {user.isAdmin && (\n                              <Link\n                                href=\"/admin\"\n                                className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\n                                onClick={() => setDropdownOpen(false)}\n                              >\n                                <Shield className=\"h-4 w-4 mr-2\" />\n                                Admin Dashboard\n                              </Link>\n                            )}\n                          </div>\n\n                          <div className=\"border-t border-gray-100\">\n                            <button\n                              onClick={handleLogout}\n                              className=\"w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 flex items-center\"\n                            >\n                              <LogOut className=\"h-4 w-4 mr-2\" />\n                              Sign out\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n                >\n                  Login\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Profile Settings Modal */}\n      {profileModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full\">\n            <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Profile Settings</h3>\n              <button\n                onClick={() => {\n                  setProfileModalOpen(false);\n                  setEditingEmail(false);\n                  setError('');\n                  setSuccess('');\n                  setNewEmail(user?.email || '');\n                }}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"p-6 space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address\n                </label>\n                {editingEmail ? (\n                  <div className=\"space-y-2\">\n                    <input\n                      type=\"email\"\n                      value={newEmail}\n                      onChange={(e) => setNewEmail(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                      placeholder=\"Enter new email\"\n                    />\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={handleUpdateEmail}\n                        disabled={isLoading}\n                        className=\"px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 disabled:opacity-50\"\n                      >\n                        {isLoading ? 'Saving...' : 'Save'}\n                      </button>\n                      <button\n                        onClick={() => {\n                          setEditingEmail(false);\n                          setNewEmail(user?.email || '');\n                          setError('');\n                        }}\n                        className=\"px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400\"\n                      >\n                        Cancel\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-900\">{user?.email}</span>\n                    <button\n                      onClick={() => setEditingEmail(true)}\n                      className=\"text-indigo-600 hover:text-indigo-800 text-sm\"\n                    >\n                      Edit\n                    </button>\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Role\n                </label>\n                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                  user?.isAdmin \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-gray-100 text-gray-800'\n                }`}>\n                  {user?.isAdmin ? 'Administrator' : 'User'}\n                </span>\n              </div>\n\n              {error && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-3 py-2 rounded-md text-sm\">\n                  {error}\n                </div>\n              )}\n\n              {success && (\n                <div className=\"bg-green-50 border border-green-200 text-green-600 px-3 py-2 rounded-md text-sm\">\n                  {success}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AA6Be,SAAS,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,iBAAiB,KAAK,EAAe;IAC/F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY,KAAK,KAAK;QACxB;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,IAAI,MAAM,aAAa,MAAM,OAAO;YAChD,gBAAgB;YAChB;QACF;QAEA,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,gBAAgB;gBAChB,uCAAuC;gBACvC,OAAO,QAAQ,CAAC,MAAM;YACxB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,gCACC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGpB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,OAAQ,KAAK,OAAO,GAAG,WAAW,UAAW;wCAAK,WAAU;;0DACtE,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAG,WAAU;0DACX,SAAS;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;0CACZ,qBACC;;sDAEE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;gDAGA,KAAK,OAAO,kBACX,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAOzC,8OAAC;4CAAI,WAAU;4CAAW,KAAK;;8DAC7B,8OAAC;oDACC,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sEAGrC,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;gDAGxB,8BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAqC,KAAK,KAAK;;;;;;oEAC3D,KAAK,OAAO,kBACX,8OAAC;wEAAE,WAAU;kFAA0B;;;;;;;;;;;;0EAI3C,8OAAC;gEACC,SAAS;oEACP,oBAAoB;oEACpB,gBAAgB;gEAClB;gEACA,WAAU;;kFAEV,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAIvC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB;kFAChC;;;;;;oEAGA,KAAK,OAAO,kBACX,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB;;0FAE/B,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;0EAMzC,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAUjD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCACC,SAAS;wCACP,oBAAoB;wCACpB,gBAAgB;wCAChB,SAAS;wCACT,WAAW;wCACX,YAAY,MAAM,SAAS;oCAC7B;oCACA,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;wCAG/D,6BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS;4DACT,UAAU;4DACV,WAAU;sEAET,YAAY,cAAc;;;;;;sEAE7B,8OAAC;4DACC,SAAS;gEACP,gBAAgB;gEAChB,YAAY,MAAM,SAAS;gEAC3B,SAAS;4DACX;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;iEAML,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB,MAAM;;;;;;8DACvC,8OAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOP,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAK,WAAW,CAAC,yDAAyD,EACzE,MAAM,UACF,gCACA,6BACJ;sDACC,MAAM,UAAU,kBAAkB;;;;;;;;;;;;gCAItC,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIJ,yBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;;;AASnB", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport {\n  Users,\n  Plus,\n  Shield,\n  Eye,\n  EyeOff,\n  Calendar\n} from 'lucide-react';\nimport Header from '@/components/Header';\n\ninterface User {\n  id: number;\n  email: string;\n  is_admin: boolean;\n  created_at: string;\n}\n\ninterface CurrentUser {\n  id: number;\n  email: string;\n  isAdmin: boolean;\n}\n\nexport default function AdminPage() {\n  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);\n  const [users, setUsers] = useState<User[]>([]);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [newUserEmail, setNewUserEmail] = useState('');\n  const [newUserPassword, setNewUserPassword] = useState('');\n  const [newUserIsAdmin, setNewUserIsAdmin] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const router = useRouter();\n\n  useEffect(() => {\n    fetchCurrentUser();\n  }, []);\n\n  useEffect(() => {\n    if (currentUser) {\n      if (!currentUser.isAdmin) {\n        router.push('/chat');\n        return;\n      }\n      fetchUsers();\n    }\n  }, [currentUser, router]);\n\n  const fetchCurrentUser = async () => {\n    try {\n      const response = await fetch('/api/auth/me');\n      if (response.ok) {\n        const data = await response.json();\n        setCurrentUser(data.user);\n      } else {\n        router.push('/login');\n      }\n    } catch (error) {\n      console.error('Failed to fetch user:', error);\n      router.push('/login');\n    }\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch('/api/admin/users');\n      if (response.ok) {\n        const data = await response.json();\n        setUsers(data.users);\n      } else {\n        setError('Failed to fetch users');\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n      setError('Failed to fetch users');\n    }\n  };\n\n  const createUser = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await fetch('/api/admin/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email: newUserEmail,\n          password: newUserPassword,\n          isAdmin: newUserIsAdmin,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setSuccess('User created successfully');\n        setNewUserEmail('');\n        setNewUserPassword('');\n        setNewUserIsAdmin(false);\n        setShowCreateForm(false);\n        fetchUsers();\n      } else {\n        setError(data.error || 'Failed to create user');\n      }\n    } catch (error) {\n      setError('An error occurred while creating the user');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  if (!currentUser) {\n    return (\n      <div className=\"min-h-screen bg-gray-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Shield className=\"h-12 w-12 text-indigo-600 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!currentUser.isAdmin) {\n    return (\n      <div className=\"min-h-screen bg-gray-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Shield className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Access Denied</h2>\n          <p className=\"text-gray-600\">You don't have permission to access this page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-100\">\n      {/* Header */}\n      <Header user={currentUser} title=\"Admin Dashboard\" />\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <Users className=\"h-8 w-8 text-indigo-600\" />\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{users.length}</p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <Shield className=\"h-8 w-8 text-green-600\" />\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Admin Users</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {users.filter(u => u.is_admin).length}\n                </p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <Users className=\"h-8 w-8 text-blue-600\" />\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Regular Users</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {users.filter(u => !u.is_admin).length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Users Management */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex justify-between items-center\">\n              <h2 className=\"text-lg font-medium text-gray-900\">User Management</h2>\n              <button\n                onClick={() => setShowCreateForm(!showCreateForm)}\n                className=\"flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add User\n              </button>\n            </div>\n          </div>\n\n          {/* Create User Form */}\n          {showCreateForm && (\n            <div className=\"px-6 py-4 border-b border-gray-200 bg-gray-50\">\n              <form onSubmit={createUser} className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                      Email Address\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      value={newUserEmail}\n                      onChange={(e) => setNewUserEmail(e.target.value)}\n                      required\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                      Password\n                    </label>\n                    <div className=\"mt-1 relative\">\n                      <input\n                        type={showPassword ? 'text' : 'password'}\n                        id=\"password\"\n                        value={newUserPassword}\n                        onChange={(e) => setNewUserPassword(e.target.value)}\n                        required\n                        className=\"block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                        placeholder=\"Enter password\"\n                      />\n                      <button\n                        type=\"button\"\n                        className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                        onClick={() => setShowPassword(!showPassword)}\n                      >\n                        {showPassword ? (\n                          <EyeOff className=\"h-5 w-5 text-gray-400\" />\n                        ) : (\n                          <Eye className=\"h-5 w-5 text-gray-400\" />\n                        )}\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"isAdmin\"\n                    checked={newUserIsAdmin}\n                    onChange={(e) => setNewUserIsAdmin(e.target.checked)}\n                    className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"isAdmin\" className=\"ml-2 block text-sm text-gray-900\">\n                    Grant admin privileges\n                  </label>\n                </div>\n\n                {error && (\n                  <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\">\n                    {error}\n                  </div>\n                )}\n\n                {success && (\n                  <div className=\"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm\">\n                    {success}\n                  </div>\n                )}\n\n                <div className=\"flex justify-end space-x-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowCreateForm(false);\n                      setError('');\n                      setSuccess('');\n                    }}\n                    className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={isLoading}\n                    className=\"px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    {isLoading ? 'Creating...' : 'Create User'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          )}\n\n          {/* Users Table */}\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    User\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Role\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Created\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {users.map((user) => (\n                  <tr key={user.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-8 w-8\">\n                          <div className=\"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\">\n                            <span className=\"text-sm font-medium text-indigo-600\">\n                              {user.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">{user.email}</div>\n                          <div className=\"text-sm text-gray-500\">ID: {user.id}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        user.is_admin \n                          ? 'bg-green-100 text-green-800' \n                          : 'bg-gray-100 text-gray-800'\n                      }`}>\n                        {user.is_admin ? 'Admin' : 'User'}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <Calendar className=\"h-4 w-4 mr-1\" />\n                        {formatDate(user.created_at)}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {users.length === 0 && (\n            <div className=\"text-center py-12\">\n              <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\n              <p className=\"text-gray-500\">Get started by creating your first user.</p>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAZA;;;;;;AA2Be,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,IAAI,CAAC,YAAY,OAAO,EAAE;gBACxB,OAAO,IAAI,CAAC;gBACZ;YACF;YACA;QACF;IACF,GAAG;QAAC;QAAa;KAAO;IAExB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,IAAI;YAC1B,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,UAAU;oBACV,SAAS;gBACX;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,gBAAgB;gBAChB,mBAAmB;gBACnB,kBAAkB;gBAClB,kBAAkB;gBAClB;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4HAAA,CAAA,UAAM;gBAAC,MAAM;gBAAa,OAAM;;;;;;0BAGjC,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;4BAOtC,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,UAAU;oCAAY,WAAU;;sDACpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA0C;;;;;;sEAG3E,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO;4DACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAA0C;;;;;;sEAG9E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAM,eAAe,SAAS;oEAC9B,IAAG;oEACH,OAAO;oEACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oEAClD,QAAQ;oEACR,WAAU;oEACV,aAAY;;;;;;8EAEd,8OAAC;oEACC,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,gBAAgB,CAAC;8EAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;6FAElB,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,SAAS;oDACT,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,OAAO;oDACnD,WAAU;;;;;;8DAEZ,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAAmC;;;;;;;;;;;;wCAKvE,uBACC,8OAAC;4CAAI,WAAU;sDACZ;;;;;;wCAIJ,yBACC,8OAAC;4CAAI,WAAU;sDACZ;;;;;;sDAIL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;wDACP,kBAAkB;wDAClB,SAAS;wDACT,WAAW;oDACb;oDACA,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAQvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,8OAAC;4CAAM,WAAU;sDACd,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAK,WAAU;0FACb,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;kFAIvC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAqC,KAAK,KAAK;;;;;;0FAC9D,8OAAC;gFAAI,WAAU;;oFAAwB;oFAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sEAIzD,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,KAAK,QAAQ,GACT,gCACA,6BACJ;0EACC,KAAK,QAAQ,GAAG,UAAU;;;;;;;;;;;sEAG/B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,WAAW,KAAK,UAAU;;;;;;;;;;;;;mDA5BxB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;4BAqCvB,MAAM,MAAM,KAAK,mBAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}