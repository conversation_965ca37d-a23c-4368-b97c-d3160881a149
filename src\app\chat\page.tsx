'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { MessageCircle } from 'lucide-react';
import Header from '@/components/Header';

interface User {
  id: number;
  email: string;
  isAdmin: boolean;
}

export default function ChatPage() {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchUser();
  }, []);

  const fetchUser = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Failed to fetch user:', error);
      router.push('/login');
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <MessageCircle className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-100 flex flex-col">
      <Header
        user={user}
        title="Chat"
        onMenuToggle={() => {}}
        showMenuButton={true}
      />

      <div className="flex-1 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
          <MessageCircle className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Chat Interface</h2>
          <p className="text-gray-600 mb-6">
            Welcome to Cobra AI Chat! The full chat interface will be restored soon.
          </p>
          <div className="space-y-2 text-left">
            <p className="text-sm text-green-600">✅ Authentication working</p>
            <p className="text-sm text-green-600">✅ Profile management working</p>
            <p className="text-sm text-green-600">✅ Password updates working</p>
            <p className="text-sm text-green-600">✅ Admin dashboard working</p>
            <p className="text-sm text-blue-600">🔄 Chat interface being restored</p>
          </div>
        </div>
      </div>
    </div>
  );
}
