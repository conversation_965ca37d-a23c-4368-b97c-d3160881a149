import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, hashPassword } from '@/lib/auth';
import { sql } from '@/lib/db';
import { validateEmail, validatePassword } from '@/lib/validation';

export async function PATCH(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      );
    }
    
    const payload = verifyToken(token);
    
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }
    
    const { email, password, currentPassword } = await request.json();

    // Validate that at least one field is provided
    if (!email && !password) {
      return NextResponse.json(
        { error: 'Email or password is required' },
        { status: 400 }
      );
    }

    // If updating password, validate current password first
    if (password) {
      if (!currentPassword) {
        return NextResponse.json(
          { error: 'Current password is required to update password' },
          { status: 400 }
        );
      }

      // Verify current password
      const userResult = await sql`
        SELECT password_hash FROM users WHERE id = ${payload.userId}
      `;

      if (userResult.length === 0) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      const bcrypt = require('bcryptjs');
      const isValidPassword = await bcrypt.compare(currentPassword, userResult[0].password_hash);

      if (!isValidPassword) {
        return NextResponse.json(
          { error: 'Current password is incorrect' },
          { status: 400 }
        );
      }

      // Validate new password
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        return NextResponse.json(
          { error: passwordValidation.errors.join(', ') },
          { status: 400 }
        );
      }
    }

    // Validate email if provided
    if (email) {
      if (!validateEmail(email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        );
      }

      // Check if email is already taken by another user
      const existingUsers = await sql`
        SELECT id FROM users
        WHERE email = ${email} AND id != ${payload.userId}
      `;

      if (existingUsers.length > 0) {
        return NextResponse.json(
          { error: 'Email is already taken' },
          { status: 409 }
        );
      }
    }

    // Update user based on provided fields
    let result;

    if (email && password) {
      const hashedPassword = await hashPassword(password);
      result = await sql`
        UPDATE users
        SET email = ${email}, password_hash = ${hashedPassword}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ${payload.userId}
        RETURNING id, email, is_admin
      `;
    } else if (email) {
      result = await sql`
        UPDATE users
        SET email = ${email}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ${payload.userId}
        RETURNING id, email, is_admin
      `;
    } else if (password) {
      const hashedPassword = await hashPassword(password);
      result = await sql`
        UPDATE users
        SET password_hash = ${hashedPassword}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ${payload.userId}
        RETURNING id, email, is_admin
      `;
    }
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    const updatedUser = result[0];
    
    return NextResponse.json({
      success: true,
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        isAdmin: updatedUser.is_admin,
      },
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
