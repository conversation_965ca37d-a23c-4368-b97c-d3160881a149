# COBRA AI Systems Chat Application

A beautiful, production-ready web application with secure authentication, admin dashboard, and AI-powered chat interface.

## Features

### 🔐 Authentication & Access

- Secure login system (no registration)
- Admin dashboard for user management
- JWT-based session management
- Role-based access control

### 💬 Chat Interface

- Real-time chat with COBRA AI API
- Chat history management
- Collapsible sidebar for session management
- Responsive design for mobile and desktop

### 👨‍💼 Admin Dashboard

- User creation and management
- Admin privilege assignment
- User statistics and overview

### 🛡️ Security Features

- Password hashing with bcrypt
- HTTP-only cookies for session management
- Input validation and sanitization
- Rate limiting protection
- CSRF protection via SameSite cookies

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Neon PostgreSQL
- **Authentication**: JWT with HTTP-only cookies
- **UI Components**: Lucide React icons
- **Deployment**: Vercel-ready

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file in the root directory:

```env
# Database
DATABASE_URL="your_neon_database_url_here"

# Authentication
NEXTAUTH_SECRET="your_nextauth_secret_here"
NEXTAUTH_URL="http://localhost:3000"

# JWT Secret
JWT_SECRET="your_jwt_secret_here"

# Admin Credentials (for initial setup)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
```

### 2. Database Setup

1. Create a Neon PostgreSQL database
2. Copy the connection string to `DATABASE_URL` in `.env.local`
3. The application will automatically create the required tables on first run

### 3. Install Dependencies

```bash
npm install
```

### 4. Run Development Server

```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

### 5. Initial Login

Use the admin credentials from your `.env.local` file:

- Email: `<EMAIL>` (or your custom admin email)
- Password: `admin123` (or your custom admin password)

## Database Schema

The application creates the following tables:

### users

- `id` (SERIAL PRIMARY KEY)
- `email` (VARCHAR UNIQUE)
- `password_hash` (VARCHAR)
- `is_admin` (BOOLEAN)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### chat_sessions

- `id` (SERIAL PRIMARY KEY)
- `user_id` (INTEGER REFERENCES users)
- `title` (VARCHAR)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### chat_messages

- `id` (SERIAL PRIMARY KEY)
- `session_id` (INTEGER REFERENCES chat_sessions)
- `message` (TEXT)
- `response` (TEXT)
- `created_at` (TIMESTAMP)

## API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Chat

- `POST /api/chat` - Send message to AI
- `GET /api/chat/sessions` - Get user's chat sessions
- `POST /api/chat/sessions` - Create new chat session
- `GET /api/chat/sessions/[id]` - Get session messages
- `DELETE /api/chat/sessions/[id]` - Delete session
- `PATCH /api/chat/sessions/[id]` - Update session title

### Admin

- `GET /api/admin/users` - Get all users (admin only)
- `POST /api/admin/users` - Create new user (admin only)

## Deployment

### Vercel Deployment

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Environment Variables for Production

Make sure to set these in your Vercel dashboard:

```
DATABASE_URL=your_neon_production_database_url
NEXTAUTH_SECRET=your_production_secret
NEXTAUTH_URL=https://your-domain.vercel.app
JWT_SECRET=your_production_jwt_secret
ADMIN_EMAIL=your_admin_email
ADMIN_PASSWORD=your_admin_password
```

## Security Considerations

1. **Environment Variables**: Never commit `.env.local` to version control
2. **Database**: Use connection pooling for production
3. **Secrets**: Generate strong, unique secrets for production
4. **HTTPS**: Always use HTTPS in production
5. **Rate Limiting**: Consider implementing Redis-based rate limiting for production

## Usage

### For Regular Users

1. Login with credentials provided by admin
2. Start new chat sessions
3. Send messages to Cobra AI
4. Manage chat history in sidebar

### For Administrators

1. Login with admin credentials
2. Access admin dashboard
3. Create new user accounts
4. Manage user permissions
5. View user statistics

## Troubleshooting

### Database Connection Issues

- Verify your Neon database URL is correct
- Check if your database is active
- Ensure your IP is whitelisted in Neon

### Authentication Issues

- Check JWT_SECRET is set
- Verify NEXTAUTH_SECRET is configured
- Clear browser cookies and try again

### API Issues

- Check browser console for errors
- Verify Cobra AI API endpoint is accessible
- Check network connectivity

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
