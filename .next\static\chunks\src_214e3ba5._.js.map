{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  MessageCircle, \n  User, \n  Settings, \n  LogOut, \n  Shield, \n  ChevronDown,\n  Menu,\n  X\n} from 'lucide-react';\n\ninterface User {\n  id: number;\n  email: string;\n  isAdmin: boolean;\n}\n\ninterface HeaderProps {\n  user?: User | null;\n  title?: string;\n  onMenuToggle?: () => void;\n  showMenuButton?: boolean;\n}\n\nexport default function Header({ user, title, onMenuToggle, showMenuButton = false }: HeaderProps) {\n  const [dropdownOpen, setDropdownOpen] = useState(false);\n  const [profileModalOpen, setProfileModalOpen] = useState(false);\n  const [editingEmail, setEditingEmail] = useState(false);\n  const [editingPassword, setEditingPassword] = useState(false);\n  const [newEmail, setNewEmail] = useState('');\n  const [currentPassword, setCurrentPassword] = useState('');\n  const [newPassword, setNewPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    if (user) {\n      setNewEmail(user.email);\n    }\n  }, [user]);\n\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setDropdownOpen(false);\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const handleUpdateEmail = async () => {\n    if (!newEmail.trim() || newEmail === user?.email) {\n      setEditingEmail(false);\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await fetch('/api/auth/update-profile', {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email: newEmail }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setSuccess('Email updated successfully');\n        setEditingEmail(false);\n        // Refresh the page to update user data\n        window.location.reload();\n      } else {\n        setError(data.error || 'Failed to update email');\n      }\n    } catch (error) {\n      setError('An error occurred while updating email');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleUpdatePassword = async () => {\n    if (!currentPassword || !newPassword || !confirmPassword) {\n      setError('All password fields are required');\n      return;\n    }\n\n    if (newPassword !== confirmPassword) {\n      setError('New passwords do not match');\n      return;\n    }\n\n    if (newPassword.length < 6) {\n      setError('New password must be at least 6 characters long');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await fetch('/api/auth/update-profile', {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          password: newPassword,\n          currentPassword: currentPassword\n        }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setSuccess('Password updated successfully');\n        setEditingPassword(false);\n        setCurrentPassword('');\n        setNewPassword('');\n        setConfirmPassword('');\n      } else {\n        setError(data.error || 'Failed to update password');\n      }\n    } catch (error) {\n      setError('An error occurred while updating password');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center\">\n              {showMenuButton && (\n                <button\n                  onClick={onMenuToggle}\n                  className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 mr-2\"\n                >\n                  <Menu className=\"h-5 w-5\" />\n                </button>\n              )}\n              <Link href={user ? (user.isAdmin ? '/admin' : '/chat') : '/'} className=\"flex items-center\">\n                <MessageCircle className=\"h-8 w-8 text-indigo-600 mr-3\" />\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  {title || 'COBRA AI Systems'}\n                </h1>\n              </Link>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <>\n                  {/* Navigation Links */}\n                  <nav className=\"hidden md:flex items-center space-x-4\">\n                    <Link\n                      href=\"/chat\"\n                      className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      Chat\n                    </Link>\n                    {user.isAdmin && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center\"\n                      >\n                        <Shield className=\"h-4 w-4 mr-1\" />\n                        Admin\n                      </Link>\n                    )}\n                  </nav>\n\n                  {/* User Profile Dropdown */}\n                  <div className=\"relative\" ref={dropdownRef}>\n                    <button\n                      onClick={() => setDropdownOpen(!dropdownOpen)}\n                      className=\"flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md px-3 py-2\"\n                    >\n                      <div className=\"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\">\n                        <span className=\"text-sm font-medium text-indigo-600\">\n                          {user.email.charAt(0).toUpperCase()}\n                        </span>\n                      </div>\n                      <span className=\"hidden md:block text-sm font-medium truncate max-w-32\">\n                        {user.email}\n                      </span>\n                      <ChevronDown className=\"h-4 w-4\" />\n                    </button>\n\n                    {dropdownOpen && (\n                      <div className=\"absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50\">\n                        <div className=\"py-1\">\n                          <div className=\"px-4 py-2 border-b border-gray-100\">\n                            <p className=\"text-sm font-medium text-gray-900\">{user.email}</p>\n                            {user.isAdmin && (\n                              <p className=\"text-xs text-indigo-600\">Administrator</p>\n                            )}\n                          </div>\n                          \n                          <button\n                            onClick={() => {\n                              setProfileModalOpen(true);\n                              setDropdownOpen(false);\n                            }}\n                            className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\n                          >\n                            <Settings className=\"h-4 w-4 mr-2\" />\n                            Profile Settings\n                          </button>\n\n                          <div className=\"md:hidden border-t border-gray-100\">\n                            <Link\n                              href=\"/chat\"\n                              className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                              onClick={() => setDropdownOpen(false)}\n                            >\n                              Chat\n                            </Link>\n                            {user.isAdmin && (\n                              <Link\n                                href=\"/admin\"\n                                className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\n                                onClick={() => setDropdownOpen(false)}\n                              >\n                                <Shield className=\"h-4 w-4 mr-2\" />\n                                Admin Dashboard\n                              </Link>\n                            )}\n                          </div>\n\n                          <div className=\"border-t border-gray-100\">\n                            <button\n                              onClick={handleLogout}\n                              className=\"w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 flex items-center\"\n                            >\n                              <LogOut className=\"h-4 w-4 mr-2\" />\n                              Sign out\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n                >\n                  Login\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Profile Settings Modal */}\n      {profileModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full\">\n            <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Profile Settings</h3>\n              <button\n                onClick={() => {\n                  setProfileModalOpen(false);\n                  setEditingEmail(false);\n                  setEditingPassword(false);\n                  setError('');\n                  setSuccess('');\n                  setNewEmail(user?.email || '');\n                  setCurrentPassword('');\n                  setNewPassword('');\n                  setConfirmPassword('');\n                }}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"p-6 space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address\n                </label>\n                {editingEmail ? (\n                  <div className=\"space-y-2\">\n                    <input\n                      type=\"email\"\n                      value={newEmail}\n                      onChange={(e) => setNewEmail(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                      placeholder=\"Enter new email\"\n                    />\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={handleUpdateEmail}\n                        disabled={isLoading}\n                        className=\"px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 disabled:opacity-50\"\n                      >\n                        {isLoading ? 'Saving...' : 'Save'}\n                      </button>\n                      <button\n                        onClick={() => {\n                          setEditingEmail(false);\n                          setNewEmail(user?.email || '');\n                          setError('');\n                        }}\n                        className=\"px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400\"\n                      >\n                        Cancel\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-900\">{user?.email}</span>\n                    <button\n                      onClick={() => setEditingEmail(true)}\n                      className=\"text-indigo-600 hover:text-indigo-800 text-sm\"\n                    >\n                      Edit\n                    </button>\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Password\n                </label>\n                {editingPassword ? (\n                  <div className=\"space-y-3\">\n                    <input\n                      type=\"password\"\n                      value={currentPassword}\n                      onChange={(e) => setCurrentPassword(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                      placeholder=\"Current password\"\n                    />\n                    <input\n                      type=\"password\"\n                      value={newPassword}\n                      onChange={(e) => setNewPassword(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                      placeholder=\"New password\"\n                    />\n                    <input\n                      type=\"password\"\n                      value={confirmPassword}\n                      onChange={(e) => setConfirmPassword(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                      placeholder=\"Confirm new password\"\n                    />\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={handleUpdatePassword}\n                        disabled={isLoading}\n                        className=\"px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 disabled:opacity-50\"\n                      >\n                        {isLoading ? 'Updating...' : 'Update Password'}\n                      </button>\n                      <button\n                        onClick={() => {\n                          setEditingPassword(false);\n                          setCurrentPassword('');\n                          setNewPassword('');\n                          setConfirmPassword('');\n                          setError('');\n                        }}\n                        className=\"px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400\"\n                      >\n                        Cancel\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-900\">••••••••</span>\n                    <button\n                      onClick={() => setEditingPassword(true)}\n                      className=\"text-indigo-600 hover:text-indigo-800 text-sm\"\n                    >\n                      Change\n                    </button>\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Role\n                </label>\n                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                  user?.isAdmin\n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-gray-100 text-gray-800'\n                }`}>\n                  {user?.isAdmin ? 'Administrator' : 'User'}\n                </span>\n              </div>\n\n              {error && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-3 py-2 rounded-md text-sm\">\n                  {error}\n                </div>\n              )}\n\n              {success && (\n                <div className=\"bg-green-50 border border-green-200 text-green-600 px-3 py-2 rounded-md text-sm\">\n                  {success}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AA6Be,SAAS,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,iBAAiB,KAAK,EAAe;;IAC/F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,MAAM;gBACR,YAAY,KAAK,KAAK;YACxB;QACF;2BAAG;QAAC;KAAK;IAET,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,gBAAgB;gBAClB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,IAAI,MAAM,aAAa,MAAM,OAAO;YAChD,gBAAgB;YAChB;QACF;QAEA,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,gBAAgB;gBAChB,uCAAuC;gBACvC,OAAO,QAAQ,CAAC,MAAM;YACxB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,iBAAiB;YACxD,SAAS;YACT;QACF;QAEA,IAAI,gBAAgB,iBAAiB;YACnC,SAAS;YACT;QACF;QAEA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;oBACV,iBAAiB;gBACnB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,mBAAmB;gBACnB,mBAAmB;gBACnB,eAAe;gBACf,mBAAmB;YACrB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE;;0BACE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,gCACC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGpB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,OAAQ,KAAK,OAAO,GAAG,WAAW,UAAW;wCAAK,WAAU;;0DACtE,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAG,WAAU;0DACX,SAAS;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;0CACZ,qBACC;;sDAEE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;gDAGA,KAAK,OAAO,kBACX,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAOzC,6LAAC;4CAAI,WAAU;4CAAW,KAAK;;8DAC7B,6LAAC;oDACC,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sEAGrC,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;gDAGxB,8BACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAqC,KAAK,KAAK;;;;;;oEAC3D,KAAK,OAAO,kBACX,6LAAC;wEAAE,WAAU;kFAA0B;;;;;;;;;;;;0EAI3C,6LAAC;gEACC,SAAS;oEACP,oBAAoB;oEACpB,gBAAgB;gEAClB;gEACA,WAAU;;kFAEV,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAIvC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB;kFAChC;;;;;;oEAGA,KAAK,OAAO,kBACX,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB;;0FAE/B,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;0EAMzC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAUjD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCACC,SAAS;wCACP,oBAAoB;wCACpB,gBAAgB;wCAChB,mBAAmB;wCACnB,SAAS;wCACT,WAAW;wCACX,YAAY,MAAM,SAAS;wCAC3B,mBAAmB;wCACnB,eAAe;wCACf,mBAAmB;oCACrB;oCACA,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;wCAG/D,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS;4DACT,UAAU;4DACV,WAAU;sEAET,YAAY,cAAc;;;;;;sEAE7B,6LAAC;4DACC,SAAS;gEACP,gBAAgB;gEAChB,YAAY,MAAM,SAAS;gEAC3B,SAAS;4DACX;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;iEAML,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAiB,MAAM;;;;;;8DACvC,6LAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOP,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;wCAG/D,gCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oDAClD,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oDAClD,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS;4DACT,UAAU;4DACV,WAAU;sEAET,YAAY,gBAAgB;;;;;;sEAE/B,6LAAC;4DACC,SAAS;gEACP,mBAAmB;gEACnB,mBAAmB;gEACnB,eAAe;gEACf,mBAAmB;gEACnB,SAAS;4DACX;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;iEAML,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOP,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAK,WAAW,CAAC,yDAAyD,EACzE,MAAM,UACF,gCACA,6BACJ;sDACC,MAAM,UAAU,kBAAkB;;;;;;;;;;;;gCAItC,uBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIJ,yBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;;;AASnB;GArawB;;QAaP,qIAAA,CAAA,YAAS;;;KAbF", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/components/MarkdownMessage.tsx"], "sourcesContent": ["'use client';\n\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\n\ninterface MarkdownMessageProps {\n  content: string;\n  className?: string;\n}\n\nexport default function MarkdownMessage({ content, className = '' }: MarkdownMessageProps) {\n  return (\n    <div className={`prose prose-sm max-w-none ${className}`}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        components={{\n          // Customize heading styles\n          h1: ({ children }) => (\n            <h1 className=\"text-lg font-bold text-gray-900 mb-2 mt-4 first:mt-0\">\n              {children}\n            </h1>\n          ),\n          h2: ({ children }) => (\n            <h2 className=\"text-base font-semibold text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h2>\n          ),\n          h3: ({ children }) => (\n            <h3 className=\"text-sm font-semibold text-gray-900 mb-1 mt-2 first:mt-0\">\n              {children}\n            </h3>\n          ),\n          \n          // Customize paragraph styles\n          p: ({ children }) => (\n            <p className=\"text-gray-900 mb-2 last:mb-0 leading-relaxed\">\n              {children}\n            </p>\n          ),\n          \n          // Customize list styles\n          ul: ({ children }) => (\n            <ul className=\"list-disc list-inside mb-2 space-y-1 text-gray-900\">\n              {children}\n            </ul>\n          ),\n          ol: ({ children }) => (\n            <ol className=\"list-decimal list-inside mb-2 space-y-1 text-gray-900\">\n              {children}\n            </ol>\n          ),\n          li: ({ children }) => (\n            <li className=\"text-gray-900\">{children}</li>\n          ),\n          \n          // Customize code styles\n          code: ({ inline, children }) => {\n            if (inline) {\n              return (\n                <code className=\"bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono\">\n                  {children}\n                </code>\n              );\n            }\n            return (\n              <code className=\"block bg-gray-100 text-gray-800 p-3 rounded-md text-xs font-mono overflow-x-auto mb-2\">\n                {children}\n              </code>\n            );\n          },\n          \n          // Customize pre styles (code blocks)\n          pre: ({ children }) => (\n            <pre className=\"bg-gray-900 text-gray-100 p-3 rounded-md text-xs font-mono overflow-x-auto mb-2\">\n              {children}\n            </pre>\n          ),\n          \n          // Customize blockquote styles\n          blockquote: ({ children }) => (\n            <blockquote className=\"border-l-4 border-indigo-200 pl-4 italic text-gray-700 mb-2\">\n              {children}\n            </blockquote>\n          ),\n          \n          // Customize link styles\n          a: ({ href, children }) => (\n            <a\n              href={href}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-indigo-600 hover:text-indigo-800 underline\"\n            >\n              {children}\n            </a>\n          ),\n          \n          // Customize table styles\n          table: ({ children }) => (\n            <div className=\"overflow-x-auto mb-2\">\n              <table className=\"min-w-full border border-gray-200 text-xs\">\n                {children}\n              </table>\n            </div>\n          ),\n          thead: ({ children }) => (\n            <thead className=\"bg-gray-50\">{children}</thead>\n          ),\n          th: ({ children }) => (\n            <th className=\"border border-gray-200 px-2 py-1 text-left font-semibold text-gray-900\">\n              {children}\n            </th>\n          ),\n          td: ({ children }) => (\n            <td className=\"border border-gray-200 px-2 py-1 text-gray-900\">\n              {children}\n            </td>\n          ),\n          \n          // Customize horizontal rule\n          hr: () => (\n            <hr className=\"border-gray-200 my-3\" />\n          ),\n          \n          // Customize strong/bold text\n          strong: ({ children }) => (\n            <strong className=\"font-semibold text-gray-900\">{children}</strong>\n          ),\n          \n          // Customize emphasis/italic text\n          em: ({ children }) => (\n            <em className=\"italic text-gray-900\">{children}</em>\n          ),\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,gBAAgB,EAAE,OAAO,EAAE,YAAY,EAAE,EAAwB;IACvF,qBACE,6LAAC;QAAI,WAAW,CAAC,0BAA0B,EAAE,WAAW;kBACtD,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,YAAY;gBACV,2BAA2B;gBAC3B,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,6BAA6B;gBAC7B,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;wBAAE,WAAU;kCACV;;;;;;gBAIL,wBAAwB;gBACxB,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAAiB;;;;;;gBAGjC,wBAAwB;gBACxB,MAAM,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;oBACzB,IAAI,QAAQ;wBACV,qBACE,6LAAC;4BAAK,WAAU;sCACb;;;;;;oBAGP;oBACA,qBACE,6LAAC;wBAAK,WAAU;kCACb;;;;;;gBAGP;gBAEA,qCAAqC;gBACrC,KAAK,CAAC,EAAE,QAAQ,EAAE,iBAChB,6LAAC;wBAAI,WAAU;kCACZ;;;;;;gBAIL,8BAA8B;gBAC9B,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;wBAAW,WAAU;kCACnB;;;;;;gBAIL,wBAAwB;gBACxB,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,iBACpB,6LAAC;wBACC,MAAM;wBACN,QAAO;wBACP,KAAI;wBACJ,WAAU;kCAET;;;;;;gBAIL,yBAAyB;gBACzB,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;sCACd;;;;;;;;;;;gBAIP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCAAc;;;;;;gBAEjC,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,4BAA4B;gBAC5B,IAAI,kBACF,6LAAC;wBAAG,WAAU;;;;;;gBAGhB,6BAA6B;gBAC7B,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,6LAAC;wBAAO,WAAU;kCAA+B;;;;;;gBAGnD,iCAAiC;gBACjC,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;YAE1C;sBAEC;;;;;;;;;;;AAIT;KAjIwB", "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/app/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { useRouter } from 'next/navigation';\nimport {\n  MessageCircle,\n  Send,\n  Plus,\n  X,\n  Trash2,\n  Edit2\n} from 'lucide-react';\nimport Header from '@/components/Header';\nimport MarkdownMessage from '@/components/MarkdownMessage';\n\ninterface Message {\n  id: number;\n  message: string;\n  response: string;\n  created_at: string;\n}\n\ninterface Session {\n  id: number;\n  title: string;\n  created_at: string;\n  updated_at: string;\n  message_count: number;\n}\n\ninterface User {\n  id: number;\n  email: string;\n  isAdmin: boolean;\n}\n\nexport default function ChatPage() {\n  const [user, setUser] = useState<User | null>(null);\n  const [sessions, setSessions] = useState<Session[]>([]);\n  const [currentSession, setCurrentSession] = useState<Session | null>(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [editingSessionId, setEditingSessionId] = useState<number | null>(null);\n  const [editingTitle, setEditingTitle] = useState('');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const router = useRouter();\n\n  // Fetch user data\n  useEffect(() => {\n    fetchUser();\n  }, []);\n\n  // Fetch sessions when user is loaded\n  useEffect(() => {\n    if (user) {\n      fetchSessions();\n    }\n  }, [user]);\n\n  // Scroll to bottom when messages change\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  const fetchUser = async () => {\n    try {\n      const response = await fetch('/api/auth/me');\n      if (response.ok) {\n        const data = await response.json();\n        setUser(data.user);\n      } else {\n        router.push('/login');\n      }\n    } catch (error) {\n      console.error('Failed to fetch user:', error);\n      router.push('/login');\n    }\n  };\n\n  const fetchSessions = async () => {\n    try {\n      const response = await fetch('/api/chat/sessions');\n      if (response.ok) {\n        const data = await response.json();\n        setSessions(data.sessions);\n      }\n    } catch (error) {\n      console.error('Failed to fetch sessions:', error);\n    }\n  };\n\n  const fetchMessages = async (sessionId: number) => {\n    try {\n      const response = await fetch(`/api/chat/sessions/${sessionId}`);\n      if (response.ok) {\n        const data = await response.json();\n        setMessages(data.messages);\n      }\n    } catch (error) {\n      console.error('Failed to fetch messages:', error);\n    }\n  };\n\n  const createNewSession = async () => {\n    try {\n      const response = await fetch('/api/chat/sessions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ title: 'New Chat' }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        const newSession = { ...data.session, message_count: 0 };\n        setSessions([newSession, ...sessions]);\n        setCurrentSession(newSession);\n        setMessages([]);\n        setSidebarOpen(false);\n      }\n    } catch (error) {\n      console.error('Failed to create session:', error);\n    }\n  };\n\n  const selectSession = (session: Session) => {\n    setCurrentSession(session);\n    fetchMessages(session.id);\n    setSidebarOpen(false);\n  };\n\n  const deleteSession = async (sessionId: number) => {\n    try {\n      const response = await fetch(`/api/chat/sessions/${sessionId}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        setSessions(sessions.filter(s => s.id !== sessionId));\n        if (currentSession?.id === sessionId) {\n          setCurrentSession(null);\n          setMessages([]);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to delete session:', error);\n    }\n  };\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <MessageCircle className=\"h-12 w-12 text-indigo-600 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const updateSessionTitle = async (sessionId: number, title: string) => {\n    try {\n      const response = await fetch(`/api/chat/sessions/${sessionId}`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ title }),\n      });\n\n      if (response.ok) {\n        setSessions(sessions.map(s =>\n          s.id === sessionId ? { ...s, title } : s\n        ));\n        if (currentSession?.id === sessionId) {\n          setCurrentSession({ ...currentSession, title });\n        }\n        setEditingSessionId(null);\n      }\n    } catch (error) {\n      console.error('Failed to update session:', error);\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const message = inputMessage.trim();\n    setInputMessage('');\n    setIsLoading(true);\n\n    // Add user message to UI immediately\n    const tempMessage = {\n      id: Date.now(),\n      message,\n      response: '',\n      created_at: new Date().toISOString(),\n    };\n    setMessages(prev => [...prev, tempMessage]);\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          question: message,\n          sessionId: currentSession?.id,\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n\n        // Update the temporary message with the response\n        setMessages(prev =>\n          prev.map(msg =>\n            msg.id === tempMessage.id\n              ? { ...msg, response: data.response }\n              : msg\n          )\n        );\n\n        // Refresh sessions to update message count\n        fetchSessions();\n      } else {\n        // Remove the temporary message on error\n        setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));\n        alert('Failed to send message');\n      }\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));\n      alert('Failed to send message');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-100 flex flex-col\">\n      <Header\n        user={user}\n        title=\"Chat\"\n        onMenuToggle={() => setSidebarOpen(true)}\n        showMenuButton={true}\n      />\n\n      <div className=\"flex-1 flex overflow-hidden\">\n        {/* Sidebar */}\n        <div className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:top-auto ${!sidebarOpen ? 'lg:w-0 lg:overflow-hidden' : 'lg:w-64'}`}>\n          <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <MessageCircle className=\"h-8 w-8 text-indigo-600 mr-2\" />\n              <h1 className=\"text-lg font-semibold text-gray-900\">Chat History</h1>\n            </div>\n            <button\n              type=\"button\"\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          </div>\n\n          <div className=\"flex-1 flex flex-col overflow-hidden\">\n            {/* New Chat Button */}\n            <div className=\"p-4\">\n              <button\n                type=\"button\"\n                onClick={createNewSession}\n                className=\"w-full flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                New Chat\n              </button>\n            </div>\n\n            {/* Sessions List */}\n            <div className=\"flex-1 overflow-y-auto px-4 pb-4\">\n              {sessions.map((session) => (\n                <div\n                  key={session.id}\n                  className={`group mb-2 p-3 rounded-lg cursor-pointer transition-colors ${\n                    currentSession?.id === session.id\n                      ? 'bg-indigo-50 border border-indigo-200'\n                      : 'hover:bg-gray-50'\n                  }`}\n                  onClick={() => selectSession(session)}\n                >\n                  {editingSessionId === session.id ? (\n                    <input\n                      type=\"text\"\n                      value={editingTitle}\n                      onChange={(e) => setEditingTitle(e.target.value)}\n                      onBlur={() => updateSessionTitle(session.id, editingTitle)}\n                      onKeyPress={(e) => {\n                        if (e.key === 'Enter') {\n                          updateSessionTitle(session.id, editingTitle);\n                        }\n                      }}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded\"\n                      autoFocus\n                    />\n                  ) : (\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {session.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {session.message_count} messages\n                        </p>\n                      </div>\n                      <div className=\"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        <button\n                          type=\"button\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setEditingSessionId(session.id);\n                            setEditingTitle(session.title);\n                          }}\n                          className=\"p-1 text-gray-400 hover:text-gray-600\"\n                          title=\"Edit session title\"\n                          aria-label=\"Edit session title\"\n                        >\n                          <Edit2 className=\"h-3 w-3\" />\n                        </button>\n                        <button\n                          type=\"button\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            deleteSession(session.id);\n                          }}\n                          className=\"p-1 text-gray-400 hover:text-red-600\"\n                          title=\"Delete session\"\n                          aria-label=\"Delete session\"\n                        >\n                          <Trash2 className=\"h-3 w-3\" />\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* Chat Header with Sidebar Toggle */}\n          <div className=\"bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <button\n                type=\"button\"\n                onClick={() => setSidebarOpen(!sidebarOpen)}\n                className=\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors\"\n                title={sidebarOpen ? \"Hide sidebar\" : \"Show sidebar\"}\n                aria-label={sidebarOpen ? \"Hide sidebar\" : \"Show sidebar\"}\n              >\n                <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n              <h2 className=\"ml-3 text-lg font-semibold text-gray-900\">\n                {currentSession ? currentSession.title : 'COBRA AI Systems'}\n              </h2>\n            </div>\n            {currentSession && (\n              <div className=\"text-sm text-gray-500\">\n                {messages.length} messages\n              </div>\n            )}\n          </div>\n\n          {/* Messages */}\n          <div className=\"flex-1 overflow-y-auto p-6\">\n            <div className=\"max-w-4xl mx-auto space-y-6\">\n            {!currentSession ? (\n              <div className=\"text-center py-12\">\n                <MessageCircle className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Welcome to COBRA AI Systems\n                </h3>\n                <p className=\"text-gray-500 mb-4\">\n                  Start a new conversation or select an existing chat from the sidebar.\n                </p>\n                <button\n                  type=\"button\"\n                  onClick={createNewSession}\n                  className=\"inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Start New Chat\n                </button>\n              </div>\n            ) : (\n              <>\n                {messages.map((msg) => (\n                  <div key={msg.id} className=\"space-y-4\">\n                    {/* User Message */}\n                    <div className=\"flex justify-end\">\n                      <div className=\"max-w-sm md:max-w-md lg:max-w-2xl xl:max-w-3xl px-4 py-3 bg-indigo-600 text-white rounded-lg shadow-sm\">\n                        <p className=\"text-sm leading-relaxed\">{msg.message}</p>\n                      </div>\n                    </div>\n\n                    {/* AI Response */}\n                    {msg.response && (\n                      <div className=\"flex justify-start\">\n                        <div className=\"max-w-sm md:max-w-md lg:max-w-2xl xl:max-w-4xl px-5 py-4 bg-white border border-gray-200 rounded-lg shadow-sm\">\n                          <MarkdownMessage content={msg.response} />\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n\n                {isLoading && (\n                  <div className=\"flex justify-start\">\n                    <div className=\"max-w-sm md:max-w-md lg:max-w-2xl xl:max-w-4xl px-5 py-4 bg-white border border-gray-200 rounded-lg shadow-sm\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"flex space-x-1\">\n                          <div className=\"w-2 h-2 bg-indigo-400 rounded-full animate-bounce\"></div>\n                          <div className=\"w-2 h-2 bg-indigo-400 rounded-full animate-bounce animate-delay-100\"></div>\n                          <div className=\"w-2 h-2 bg-indigo-400 rounded-full animate-bounce animate-delay-200\"></div>\n                        </div>\n                        <span className=\"text-sm text-gray-600\">AI is thinking...</span>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                <div ref={messagesEndRef} />\n              </>\n            )}\n            </div>\n          </div>\n\n          {/* Input */}\n          {currentSession && (\n            <div className=\"bg-white border-t border-gray-200 p-4\">\n              <div className=\"max-w-4xl mx-auto\">\n                <div className=\"flex items-end space-x-3\">\n                  <div className=\"flex-1\">\n                    <textarea\n                      value={inputMessage}\n                      onChange={(e) => setInputMessage(e.target.value)}\n                      onKeyPress={handleKeyPress}\n                      placeholder=\"Type your message...\"\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none text-sm\"\n                      rows={1}\n                      disabled={isLoading}\n                    />\n                  </div>\n                  <button\n                    type=\"button\"\n                    onClick={sendMessage}\n                    disabled={!inputMessage.trim() || isLoading}\n                    className=\"p-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                    title=\"Send message\"\n                    aria-label=\"Send message\"\n                  >\n                    <Send className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Sidebar Overlay */}\n        {sidebarOpen && (\n          <div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;;;AAbA;;;;;;AAoCe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;6BAAG;QAAC;KAAK;IAET,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;6BAAG;QAAC;KAAS;IAEb,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;YACnB,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,WAAW;YAC9D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO;gBAAW;YAC3C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,aAAa;oBAAE,GAAG,KAAK,OAAO;oBAAE,eAAe;gBAAE;gBACvD,YAAY;oBAAC;uBAAe;iBAAS;gBACrC,kBAAkB;gBAClB,YAAY,EAAE;gBACd,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,kBAAkB;QAClB,cAAc,QAAQ,EAAE;QACxB,eAAe;IACjB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,WAAW,EAAE;gBAC9D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC1C,IAAI,gBAAgB,OAAO,WAAW;oBACpC,kBAAkB;oBAClB,YAAY,EAAE;gBAChB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,WAAW,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,GAAG,CAAC,CAAA,IACvB,EAAE,EAAE,KAAK,YAAY;wBAAE,GAAG,CAAC;wBAAE;oBAAM,IAAI;gBAEzC,IAAI,gBAAgB,OAAO,WAAW;oBACpC,kBAAkB;wBAAE,GAAG,cAAc;wBAAE;oBAAM;gBAC/C;gBACA,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,UAAU,aAAa,IAAI;QACjC,gBAAgB;QAChB,aAAa;QAEb,qCAAqC;QACrC,MAAM,cAAc;YAClB,IAAI,KAAK,GAAG;YACZ;YACA,UAAU;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAE1C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;oBACV,WAAW,gBAAgB;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,iDAAiD;gBACjD,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,YAAY,EAAE,GACrB;4BAAE,GAAG,GAAG;4BAAE,UAAU,KAAK,QAAQ;wBAAC,IAClC;gBAIR,2CAA2C;gBAC3C;YACF,OAAO;gBACL,wCAAwC;gBACxC,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY,EAAE;gBAChE,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY,EAAE;YAChE,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;gBACL,MAAM;gBACN,OAAM;gBACN,cAAc,IAAM,eAAe;gBACnC,gBAAgB;;;;;;0BAGlB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,GAAG,cAAc,kBAAkB,oBAAoB,+JAA+J,EAAE,CAAC,cAAc,8BAA8B,WAAW;;0CAC9R,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAEtD,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMrC,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gDAEC,WAAW,CAAC,2DAA2D,EACrE,gBAAgB,OAAO,QAAQ,EAAE,GAC7B,0CACA,oBACJ;gDACF,SAAS,IAAM,cAAc;0DAE5B,qBAAqB,QAAQ,EAAE,iBAC9B,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,QAAQ,IAAM,mBAAmB,QAAQ,EAAE,EAAE;oDAC7C,YAAY,CAAC;wDACX,IAAI,EAAE,GAAG,KAAK,SAAS;4DACrB,mBAAmB,QAAQ,EAAE,EAAE;wDACjC;oDACF;oDACA,WAAU;oDACV,SAAS;;;;;yEAGX,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,QAAQ,KAAK;;;;;;8EAEhB,6LAAC;oEAAE,WAAU;;wEACV,QAAQ,aAAa;wEAAC;;;;;;;;;;;;;sEAG3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,oBAAoB,QAAQ,EAAE;wEAC9B,gBAAgB,QAAQ,KAAK;oEAC/B;oEACA,WAAU;oEACV,OAAM;oEACN,cAAW;8EAEX,cAAA,6LAAC,qMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6LAAC;oEACC,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,cAAc,QAAQ,EAAE;oEAC1B;oEACA,WAAU;oEACV,OAAM;oEACN,cAAW;8EAEX,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAxDrB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAoEzB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,eAAe,CAAC;gDAC/B,WAAU;gDACV,OAAO,cAAc,iBAAiB;gDACtC,cAAY,cAAc,iBAAiB;0DAE3C,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9D,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DACX,iBAAiB,eAAe,KAAK,GAAG;;;;;;;;;;;;oCAG5C,gCACC,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,MAAM;4CAAC;;;;;;;;;;;;;0CAMvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACd,CAAC,+BACA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;6DAKrC;;4CACG,SAAS,GAAG,CAAC,CAAC,oBACb,6LAAC;oDAAiB,WAAU;;sEAE1B,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAU;8EAA2B,IAAI,OAAO;;;;;;;;;;;;;;;;wDAKtD,IAAI,QAAQ,kBACX,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;oEAAC,SAAS,IAAI,QAAQ;;;;;;;;;;;;;;;;;mDAZpC,IAAI,EAAE;;;;;4CAmBjB,2BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;0EAEjB,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;0DAMhD,6LAAC;gDAAI,KAAK;;;;;;;;;;;;;;;;;;4BAOf,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,YAAY;oDACZ,aAAY;oDACZ,WAAU;oDACV,MAAM;oDACN,UAAU;;;;;;;;;;;0DAGd,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,aAAa,IAAI,MAAM;gDAClC,WAAU;gDACV,OAAM;gDACN,cAAW;0DAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS3B,6BACC,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,eAAe;;;;;;;;;;;;;;;;;;AAM1C;GAzcwB;;QAWP,qIAAA,CAAA,YAAS;;;KAXF", "debugId": null}}]}