import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { sql } from './db';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';

export interface User {
  id: number;
  email: string;
  is_admin: boolean;
}

export interface JWTPayload {
  userId: number;
  email: string;
  isAdmin: boolean;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate JWT token
export function generateToken(user: User): string {
  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    isAdmin: user.is_admin,
  };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
}

// Verify JWT token
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch (error) {
    return null;
  }
}

// Authenticate user
export async function authenticateUser(email: string, password: string): Promise<User | null> {
  try {
    const users = await sql`
      SELECT id, email, password_hash, is_admin 
      FROM users 
      WHERE email = ${email}
    `;
    
    if (users.length === 0) {
      return null;
    }
    
    const user = users[0];
    const isValidPassword = await verifyPassword(password, user.password_hash);
    
    if (!isValidPassword) {
      return null;
    }
    
    return {
      id: user.id,
      email: user.email,
      is_admin: user.is_admin,
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

// Get user by ID
export async function getUserById(id: number): Promise<User | null> {
  try {
    const users = await sql`
      SELECT id, email, is_admin 
      FROM users 
      WHERE id = ${id}
    `;
    
    if (users.length === 0) {
      return null;
    }
    
    const user = users[0];
    return {
      id: user.id,
      email: user.email,
      is_admin: user.is_admin,
    };
  } catch (error) {
    console.error('Get user error:', error);
    return null;
  }
}

// Create new user (admin only)
export async function createUser(email: string, password: string, isAdmin: boolean = false): Promise<User | null> {
  try {
    const hashedPassword = await hashPassword(password);
    
    const result = await sql`
      INSERT INTO users (email, password_hash, is_admin)
      VALUES (${email}, ${hashedPassword}, ${isAdmin})
      RETURNING id, email, is_admin
    `;
    
    if (result.length === 0) {
      return null;
    }
    
    const user = result[0];
    return {
      id: user.id,
      email: user.email,
      is_admin: user.is_admin,
    };
  } catch (error) {
    console.error('Create user error:', error);
    return null;
  }
}
