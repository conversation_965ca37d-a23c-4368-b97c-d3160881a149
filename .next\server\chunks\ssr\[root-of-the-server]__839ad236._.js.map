{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  MessageCircle, \n  User, \n  Settings, \n  LogOut, \n  Shield, \n  ChevronDown,\n  Menu,\n  X\n} from 'lucide-react';\n\ninterface User {\n  id: number;\n  email: string;\n  isAdmin: boolean;\n}\n\ninterface HeaderProps {\n  user?: User | null;\n  title?: string;\n  onMenuToggle?: () => void;\n  showMenuButton?: boolean;\n}\n\nexport default function Header({ user, title, onMenuToggle, showMenuButton = false }: HeaderProps) {\n  const [dropdownOpen, setDropdownOpen] = useState(false);\n  const [profileModalOpen, setProfileModalOpen] = useState(false);\n  const [editingEmail, setEditingEmail] = useState(false);\n  const [newEmail, setNewEmail] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    if (user) {\n      setNewEmail(user.email);\n    }\n  }, [user]);\n\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setDropdownOpen(false);\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const handleUpdateEmail = async () => {\n    if (!newEmail.trim() || newEmail === user?.email) {\n      setEditingEmail(false);\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await fetch('/api/auth/update-profile', {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email: newEmail }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setSuccess('Email updated successfully');\n        setEditingEmail(false);\n        // Refresh the page to update user data\n        window.location.reload();\n      } else {\n        setError(data.error || 'Failed to update email');\n      }\n    } catch (error) {\n      setError('An error occurred while updating email');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center\">\n              {showMenuButton && (\n                <button\n                  onClick={onMenuToggle}\n                  className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 mr-2\"\n                >\n                  <Menu className=\"h-5 w-5\" />\n                </button>\n              )}\n              <Link href={user ? (user.isAdmin ? '/admin' : '/chat') : '/'} className=\"flex items-center\">\n                <MessageCircle className=\"h-8 w-8 text-indigo-600 mr-3\" />\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  {title || 'Cobra AI Chat'}\n                </h1>\n              </Link>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <>\n                  {/* Navigation Links */}\n                  <nav className=\"hidden md:flex items-center space-x-4\">\n                    <Link\n                      href=\"/chat\"\n                      className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                    >\n                      Chat\n                    </Link>\n                    {user.isAdmin && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center\"\n                      >\n                        <Shield className=\"h-4 w-4 mr-1\" />\n                        Admin\n                      </Link>\n                    )}\n                  </nav>\n\n                  {/* User Profile Dropdown */}\n                  <div className=\"relative\" ref={dropdownRef}>\n                    <button\n                      onClick={() => setDropdownOpen(!dropdownOpen)}\n                      className=\"flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md px-3 py-2\"\n                    >\n                      <div className=\"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\">\n                        <span className=\"text-sm font-medium text-indigo-600\">\n                          {user.email.charAt(0).toUpperCase()}\n                        </span>\n                      </div>\n                      <span className=\"hidden md:block text-sm font-medium truncate max-w-32\">\n                        {user.email}\n                      </span>\n                      <ChevronDown className=\"h-4 w-4\" />\n                    </button>\n\n                    {dropdownOpen && (\n                      <div className=\"absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50\">\n                        <div className=\"py-1\">\n                          <div className=\"px-4 py-2 border-b border-gray-100\">\n                            <p className=\"text-sm font-medium text-gray-900\">{user.email}</p>\n                            {user.isAdmin && (\n                              <p className=\"text-xs text-indigo-600\">Administrator</p>\n                            )}\n                          </div>\n                          \n                          <button\n                            onClick={() => {\n                              setProfileModalOpen(true);\n                              setDropdownOpen(false);\n                            }}\n                            className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\n                          >\n                            <Settings className=\"h-4 w-4 mr-2\" />\n                            Profile Settings\n                          </button>\n\n                          <div className=\"md:hidden border-t border-gray-100\">\n                            <Link\n                              href=\"/chat\"\n                              className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                              onClick={() => setDropdownOpen(false)}\n                            >\n                              Chat\n                            </Link>\n                            {user.isAdmin && (\n                              <Link\n                                href=\"/admin\"\n                                className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\n                                onClick={() => setDropdownOpen(false)}\n                              >\n                                <Shield className=\"h-4 w-4 mr-2\" />\n                                Admin Dashboard\n                              </Link>\n                            )}\n                          </div>\n\n                          <div className=\"border-t border-gray-100\">\n                            <button\n                              onClick={handleLogout}\n                              className=\"w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 flex items-center\"\n                            >\n                              <LogOut className=\"h-4 w-4 mr-2\" />\n                              Sign out\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n                >\n                  Login\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Profile Settings Modal */}\n      {profileModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full\">\n            <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Profile Settings</h3>\n              <button\n                onClick={() => {\n                  setProfileModalOpen(false);\n                  setEditingEmail(false);\n                  setError('');\n                  setSuccess('');\n                  setNewEmail(user?.email || '');\n                }}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"p-6 space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address\n                </label>\n                {editingEmail ? (\n                  <div className=\"space-y-2\">\n                    <input\n                      type=\"email\"\n                      value={newEmail}\n                      onChange={(e) => setNewEmail(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                      placeholder=\"Enter new email\"\n                    />\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={handleUpdateEmail}\n                        disabled={isLoading}\n                        className=\"px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 disabled:opacity-50\"\n                      >\n                        {isLoading ? 'Saving...' : 'Save'}\n                      </button>\n                      <button\n                        onClick={() => {\n                          setEditingEmail(false);\n                          setNewEmail(user?.email || '');\n                          setError('');\n                        }}\n                        className=\"px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400\"\n                      >\n                        Cancel\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-900\">{user?.email}</span>\n                    <button\n                      onClick={() => setEditingEmail(true)}\n                      className=\"text-indigo-600 hover:text-indigo-800 text-sm\"\n                    >\n                      Edit\n                    </button>\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Role\n                </label>\n                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                  user?.isAdmin \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-gray-100 text-gray-800'\n                }`}>\n                  {user?.isAdmin ? 'Administrator' : 'User'}\n                </span>\n              </div>\n\n              {error && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-3 py-2 rounded-md text-sm\">\n                  {error}\n                </div>\n              )}\n\n              {success && (\n                <div className=\"bg-green-50 border border-green-200 text-green-600 px-3 py-2 rounded-md text-sm\">\n                  {success}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AA6Be,SAAS,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,iBAAiB,KAAK,EAAe;IAC/F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY,KAAK,KAAK;QACxB;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,IAAI,MAAM,aAAa,MAAM,OAAO;YAChD,gBAAgB;YAChB;QACF;QAEA,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,gBAAgB;gBAChB,uCAAuC;gBACvC,OAAO,QAAQ,CAAC,MAAM;YACxB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,gCACC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGpB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,OAAQ,KAAK,OAAO,GAAG,WAAW,UAAW;wCAAK,WAAU;;0DACtE,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAG,WAAU;0DACX,SAAS;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;0CACZ,qBACC;;sDAEE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;gDAGA,KAAK,OAAO,kBACX,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAOzC,8OAAC;4CAAI,WAAU;4CAAW,KAAK;;8DAC7B,8OAAC;oDACC,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sEAGrC,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;gDAGxB,8BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAqC,KAAK,KAAK;;;;;;oEAC3D,KAAK,OAAO,kBACX,8OAAC;wEAAE,WAAU;kFAA0B;;;;;;;;;;;;0EAI3C,8OAAC;gEACC,SAAS;oEACP,oBAAoB;oEACpB,gBAAgB;gEAClB;gEACA,WAAU;;kFAEV,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAIvC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB;kFAChC;;;;;;oEAGA,KAAK,OAAO,kBACX,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB;;0FAE/B,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;0EAMzC,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAUjD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCACC,SAAS;wCACP,oBAAoB;wCACpB,gBAAgB;wCAChB,SAAS;wCACT,WAAW;wCACX,YAAY,MAAM,SAAS;oCAC7B;oCACA,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;wCAG/D,6BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS;4DACT,UAAU;4DACV,WAAU;sEAET,YAAY,cAAc;;;;;;sEAE7B,8OAAC;4DACC,SAAS;gEACP,gBAAgB;gEAChB,YAAY,MAAM,SAAS;gEAC3B,SAAS;4DACX;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;iEAML,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB,MAAM;;;;;;8DACvC,8OAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOP,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAK,WAAW,CAAC,yDAAyD,EACzE,MAAM,UACF,gCACA,6BACJ;sDACC,MAAM,UAAU,kBAAkB;;;;;;;;;;;;gCAItC,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIJ,yBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;;;AASnB", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent_team/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { MessageCircle, Eye, EyeOff } from 'lucide-react';\nimport Header from '@/components/Header';\n\nexport default function LoginPage() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const router = useRouter();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n      console.log('Login response:', response.status, data);\n\n      if (response.ok) {\n        console.log('Login successful, user:', data.user);\n        // Small delay to ensure cookie is set\n        setTimeout(() => {\n          // Redirect based on user role\n          if (data.user.isAdmin) {\n            console.log('Redirecting to admin...');\n            window.location.href = '/admin';\n          } else {\n            console.log('Redirecting to chat...');\n            window.location.href = '/chat';\n          }\n        }, 100);\n      } else {\n        console.log('Login failed:', data);\n        setError(data.error || 'Login failed');\n      }\n    } catch (error) {\n      setError('An error occurred. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <Header />\n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        {/* Header */}\n        <div className=\"text-center\">\n          <Link href=\"/\" className=\"inline-flex items-center\">\n            <MessageCircle className=\"h-12 w-12 text-indigo-600 mr-3\" />\n            <h1 className=\"text-3xl font-bold text-gray-900\">Cobra AI Chat</h1>\n          </Link>\n          <h2 className=\"mt-6 text-2xl font-bold text-gray-900\">\n            Sign in to your account\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Enter your credentials to access the chat system\n          </p>\n        </div>\n\n        {/* Login Form */}\n        <div className=\"bg-white py-8 px-6 shadow-lg rounded-lg\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\">\n                {error}\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeOff className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <Eye className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? 'Signing in...' : 'Sign in'}\n              </button>\n            </div>\n          </form>\n\n          {/* Demo Credentials */}\n          <div className=\"mt-6 p-4 bg-gray-50 rounded-md\">\n            <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Demo Credentials:</h3>\n            <div className=\"text-xs text-gray-600 space-y-1\">\n              <p><strong>Admin:</strong> <EMAIL> / admin123</p>\n              <p><em>Note: Admin can create new user accounts</em></p>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center\">\n          <Link\n            href=\"/\"\n            className=\"text-indigo-600 hover:text-indigo-500 text-sm font-medium\"\n          >\n            ← Back to homepage\n          </Link>\n        </div>\n      </div>\n    </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,mBAAmB,SAAS,MAAM,EAAE;YAEhD,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,2BAA2B,KAAK,IAAI;gBAChD,sCAAsC;gBACtC,WAAW;oBACT,8BAA8B;oBAC9B,IAAI,KAAK,IAAI,CAAC,OAAO,EAAE;wBACrB,QAAQ,GAAG,CAAC;wBACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF,GAAG;YACL,OAAO;gBACL,QAAQ,GAAG,CAAC,iBAAiB;gBAC7B,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;oCAAY,UAAU;;wCACnC,uBACC,8OAAC;4CAAI,WAAU;sDACZ;;;;;;sDAIL,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA0C;;;;;;8DAG3E,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM,eAAe,SAAS;4DAC9B,cAAa;4DACb,QAAQ;4DACR,OAAO;4DACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,gBAAgB,CAAC;sEAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMvB,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;8CAMrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAC1B,8OAAC;8DAAE,cAAA,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQX", "debugId": null}}]}